// Chat-related TypeScript interfaces and types

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: {
    id: number;
    name: string;
    guard_name: string;
  };
}

export interface ChatParticipant {
  id: string;
  name: string;
  avatar?: string;
  type: 'customer' | 'provider' | 'admin';
  email?: string;
  last_seen?: string;
  is_online?: boolean;
}

export interface Message {
  id: string;
  chat_id: string;
  user_id: string;
  type: 'text' | 'image' | 'file';
  message: string;
  created_at: string;
  updated_at: string;
  read_at?: string;
  user: ChatParticipant;
}

export interface Chat {
  id: string;
  type: 'direct' | 'group';
  name?: string;
  description?: string;
  created_at: string;
  updated_at: string;
  participants: ChatParticipant[];
  last_message?: Message;
  unread_count?: number;
}

export interface ChatListResponse {
  data: Chat[];
  meta?: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface MessagesResponse {
  data: Message[];
  meta?: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export interface SendMessageRequest {
  type: 'text' | 'image' | 'file';
  message: string;
}

export interface SendMessageResponse {
  data: Message;
  success: boolean;
  message?: string;
}

// WebSocket message types
export interface WebSocketMessage {
  action: 'join' | 'message_sent' | 'user_typing' | 'user_online' | 'user_offline';
  chat_id?: string;
  data?: any;
}

export interface JoinChatMessage extends WebSocketMessage {
  action: 'join';
  chat_id: string;
}

export interface MessageSentMessage extends WebSocketMessage {
  action: 'message_sent';
  data: Message;
}

export interface UserTypingMessage extends WebSocketMessage {
  action: 'user_typing';
  chat_id: string;
  data: {
    user_id: string;
    is_typing: boolean;
  };
}

// Enhanced Chat context types for universal chat
export interface ChatState {
  currentUser: ChatParticipant;
  activeChats: Chat[];
  selectedChat: Chat | null;
  messages: Message[];
  userRole: 'admin' | 'provider' | 'customer';
  permissions: import('../components/chat/config/roleConfigurations').ChatFeatures;
  theme: import('../components/chat/config/roleConfigurations').ChatTheme;
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;
  typingUsers: Record<string, string[]>; // chatId -> userIds
  onlineUsers: string[];
}

export type ChatAction =
  | { type: 'SET_CURRENT_USER'; payload: ChatParticipant }
  | { type: 'SET_USER_ROLE'; payload: 'admin' | 'provider' | 'customer' }
  | { type: 'SET_PERMISSIONS'; payload: import('../components/chat/config/roleConfigurations').ChatFeatures }
  | { type: 'SET_THEME'; payload: import('../components/chat/config/roleConfigurations').ChatTheme }
  | { type: 'SET_CHATS'; payload: Chat[] }
  | { type: 'ADD_CHAT'; payload: Chat }
  | { type: 'UPDATE_CHAT'; payload: { chatId: string; updates: Partial<Chat> } }
  | { type: 'REMOVE_CHAT'; payload: string }
  | { type: 'SELECT_CHAT'; payload: string | null }
  | { type: 'SET_MESSAGES'; payload: Message[] }
  | { type: 'ADD_MESSAGE'; payload: Message }
  | { type: 'UPDATE_MESSAGE'; payload: { messageId: string; updates: Partial<Message> } }
  | { type: 'REMOVE_MESSAGE'; payload: string }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_CONNECTED'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_TYPING_USERS'; payload: { chatId: string; userIds: string[] } }
  | { type: 'SET_ONLINE_USERS'; payload: string[] }
  | { type: 'UPDATE_CHAT_LAST_MESSAGE'; payload: { chatId: string; message: Message } };

export interface ChatContextType {
  // State
  state: ChatState;
  dispatch: React.Dispatch<ChatAction>;

  // Legacy compatibility - these will delegate to the new state/dispatch pattern
  chats: Chat[];
  currentChat: Chat | null;
  messages: Message[];
  isLoading: boolean;
  isConnected: boolean;
  error: string | null;

  // Actions
  loadChats: () => Promise<void>;
  loadMessages: (chatId: string, page?: number) => Promise<void>;
  sendMessage: (chatId: string, message: SendMessageRequest) => Promise<void>;
  joinChat: (chatId: string) => void;
  leaveChat: () => void;
  markAsRead: (chatId: string) => Promise<void>;
  deleteMessage: (chatId: string, messageId: string) => Promise<void>;

  // WebSocket
  connect: () => void;
  disconnect: () => void;
  reconnect: () => void;

  // New universal chat methods
  initializeForRole: (userRole: 'admin' | 'provider' | 'customer', user: ChatParticipant) => void;
  createChat: (participantIds: string[], type?: 'direct' | 'group', name?: string) => Promise<void>;
  updateChatSettings: (chatId: string, settings: Partial<Chat>) => Promise<void>;
  pinMessage: (chatId: string, messageId: string) => Promise<void>;
  unpinMessage: (chatId: string, messageId: string) => Promise<void>;
  muteChat: (chatId: string) => Promise<void>;
  unmuteChat: (chatId: string) => Promise<void>;
  blockUser: (userId: string) => Promise<void>;
  unblockUser: (userId: string) => Promise<void>;
  reportMessage: (chatId: string, messageId: string, reason: string) => Promise<void>;
}

// Hook return types
export type UseChatReturn = ChatContextType

// Error types
export interface ChatError {
  code: string;
  message: string;
  details?: any;
}


// Universal Chat Component Props
export interface UniversalChatProps {
  userRole: 'admin' | 'provider' | 'customer';
  userId: string;
  recipientId?: string;
  chatId?: string;
  variant?: 'compact' | 'full' | 'modal';
  features?: Partial<import('../components/chat/config/roleConfigurations').ChatFeatures>;
  theme?: 'default' | 'admin' | 'provider' | 'customer' | 'custom';
  customTheme?: Partial<import('../components/chat/config/roleConfigurations').ChatTheme>;
  className?: string;
  onChatSelect?: (chatId: string) => void;
  onMessageSent?: (message: Message) => void;
  onError?: (error: string) => void;
}

// Chat Settings
export interface ChatSettings {
  isMuted: boolean;
  notifications: boolean;
  theme: 'light' | 'dark' | 'auto';
  fontSize: 'small' | 'medium' | 'large';
  soundEnabled: boolean;
  enterToSend: boolean;
  showTimestamps: boolean;
  showReadReceipts: boolean;
  autoDownloadMedia: boolean;
}

// Enhanced Message with additional features
export interface EnhancedMessage extends Message {
  isPinned?: boolean;
  isEdited?: boolean;
  editedAt?: string;
  replyTo?: string; // Message ID this is replying to
  reactions?: MessageReaction[];
  attachments?: MessageAttachment[];
  mentions?: string[]; // User IDs mentioned in the message
  isSystemMessage?: boolean;
  metadata?: Record<string, any>;
}

export interface MessageReaction {
  emoji: string;
  users: string[]; // User IDs who reacted
  count: number;
}

export interface MessageAttachment {
  id: string;
  type: 'image' | 'file' | 'video' | 'audio';
  name: string;
  url: string;
  size: number;
  mimeType: string;
  thumbnail?: string;
  duration?: number; // For audio/video files
}

// Chat Statistics (for admin role)
export interface ChatStatistics {
  totalMessages: number;
  totalParticipants: number;
  averageResponseTime: number;
  mostActiveHour: number;
  messagesByType: Record<string, number>;
  participantActivity: Record<string, number>;
}

export interface DataChat {
    id: string;
    type: string;
    participants: ({
        id: number;
        name: string;
        email: string;
        slug: string;
        system_reserve: number;
        served: number;
        phone: null;
        code: null;
        provider_id: null;
        status: number;
        is_featured: number;
        is_verified: number;
        type: null;
        email_verified_at: null;
        fcm_token: null;
        experience_interval: null;
        experience_duration: null;
        description: null;
        created_by: null;
        created_at: string;
        updated_at: string;
        deleted_at: null;
        company_id: null;
        business_uuid: null;
        location_cordinates: null;
        certificates: never[];
        certificates_status: null;
        bookings_count: number;
        reviews_count: number;
        role: {
            id: number;
            name: string;
            guard_name: string;
            system_reserve: number;
            created_at: string;
            updated_at: string;
            pivot: { model_type: string; model_id: number; role_id: number }
        };
        review_ratings: number;
        provider_rating_list: number[];
        service_man_rating_list: number[];
        primary_address: null;
        total_days_experience: number;
        ServicemanReviewRatings: number;
        media: never[];
        wallet: null;
        provider_wallet: null;
        serviceman_wallet: null;
        known_languages: never[];
        expertise: never[];
        zones: never[];
        provider: null;
        subscriptions: never[];
        roles: {
            id: number;
            name: string;
            guard_name: string;
            system_reserve: number;
            created_at: string;
            updated_at: string;
            pivot: { model_type: string; model_id: number; role_id: number }
        }[];
        reviews: never[];
        servicemanreviews: never[];
        current_subscription: null
    } | {
        id: number;
        name: string;
        email: string;
        slug: string;
        system_reserve: number;
        served: number;
        phone: null;
        code: null;
        provider_id: null;
        status: number;
        is_featured: number;
        is_verified: number;
        type: null;
        email_verified_at: null;
        fcm_token: null;
        experience_interval: null;
        experience_duration: null;
        description: null;
        created_by: number;
        created_at: string;
        updated_at: string;
        deleted_at: null;
        company_id: null;
        business_uuid: null;
        location_cordinates: null;
        certificates: {
            uuid: string;
            file_name: string;
            url: string;
            mime_type: string;
            file_size: number;
            collection_name: string;
            custom_properties: { uploaded_by: null; original_name: string }
        }[];
        certificates_status: string;
        bookings_count: number;
        reviews_count: number;
        role: {
            id: number;
            name: string;
            guard_name: string;
            system_reserve: number;
            created_at: string;
            updated_at: string;
            pivot: { model_type: string; model_id: number; role_id: number }
        };
        review_ratings: number;
        provider_rating_list: number[];
        service_man_rating_list: number[];
        primary_address: null;
        total_days_experience: number;
        ServicemanReviewRatings: number;
        media: never[];
        wallet: null;
        provider_wallet: null;
        serviceman_wallet: null;
        known_languages: never[];
        expertise: never[];
        zones: never[];
        provider: null;
        subscriptions: never[];
        roles: {
            id: number;
            name: string;
            guard_name: string;
            system_reserve: number;
            created_at: string;
            updated_at: string;
            pivot: { model_type: string; model_id: number; role_id: number }
        }[];
        reviews: never[];
        servicemanreviews: never[];
        current_subscription: null
    })[];
    last_message: null;
    created_at: string;
    updated_at: string;
}