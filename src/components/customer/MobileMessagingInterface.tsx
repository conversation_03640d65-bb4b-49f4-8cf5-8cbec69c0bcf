import React, { useState, useRef, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MessageSquare, Send, Paperclip, Image, Plus, ArrowLeft } from 'lucide-react';
import { Badge } from "@/components/ui/badge";
import { MobileBackButton } from '@/components/admin/messaging/MobileBackButton';
import { useIsMobile } from '@/hooks/use-mobile';
import { useToast } from '@/hooks/use-toast';
import { Card } from "@/components/ui/card";
type Message = {
  id: string;
  sender: string;
  content: string;
  timestamp: string;
  isCustomer: boolean;
  status?: 'sent' | 'delivered' | 'read';
};
type Conversation = {
  id: string;
  provider: {
    name: string;
    avatar?: string;
    initials: string;
  };
  jobTitle: string;
  lastMessage: string;
  timestamp: string;
  unread: number;
  messages: Message[];
};
export function MobileMessagingInterface() {
  const {
    toast
  } = useToast();
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [messageInput, setMessageInput] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [showConversationDetail, setShowConversationDetail] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Mock conversations data - this would come from an API in a real app
  const conversations: Conversation[] = [{
    id: "conv1",
    provider: {
      name: "Mike's Plumbing",
      initials: "MP"
    },
    jobTitle: "Bathroom Renovation",
    lastMessage: "Can you confirm the appointment time?",
    timestamp: "11:42 AM",
    unread: 2,
    messages: [{
      id: "msg1",
      sender: "Mike's Plumbing",
      content: "Hi there! I've reviewed your bathroom renovation job request.",
      timestamp: "Yesterday, 2:30 PM",
      isCustomer: false,
      status: 'read'
    }, {
      id: "msg2",
      sender: "You",
      content: "Great! What do you think about the timeline?",
      timestamp: "Yesterday, 3:15 PM",
      isCustomer: true,
      status: 'read'
    }, {
      id: "msg3",
      sender: "Mike's Plumbing",
      content: "I think we can complete it within 3 weeks. Can you confirm the appointment time for the initial inspection?",
      timestamp: "Today, 11:42 AM",
      isCustomer: false,
      status: 'delivered'
    }]
  }, {
    id: "conv2",
    provider: {
      name: "Green Thumb Landscaping",
      avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?q=80&w=1964&auto=format&fit=crop",
      initials: "GT"
    },
    jobTitle: "Lawn Maintenance",
    lastMessage: "Your quote is ready to review",
    timestamp: "Yesterday",
    unread: 0,
    messages: [{
      id: "msg1",
      sender: "Green Thumb Landscaping",
      content: "Hello! I've prepared a quote for your lawn maintenance job.",
      timestamp: "Yesterday, 10:15 AM",
      isCustomer: false,
      status: 'read'
    }, {
      id: "msg2",
      sender: "You",
      content: "Thanks! I'll take a look at it.",
      timestamp: "Yesterday, 11:30 AM",
      isCustomer: true,
      status: 'read'
    }]
  }, {
    id: "conv3",
    provider: {
      name: "Elite Electricians",
      initials: "EE"
    },
    jobTitle: "Lighting Installation",
    lastMessage: "Thank you for your payment",
    timestamp: "Apr 15",
    unread: 0,
    messages: [{
      id: "msg1",
      sender: "Elite Electricians",
      content: "Thank you for your payment. We've received it successfully.",
      timestamp: "Apr 15, 9:20 AM",
      isCustomer: false,
      status: 'read'
    }]
  }];

  // Filter conversations by search query
  const filteredConversations = conversations.filter(conv => conv.provider.name.toLowerCase().includes(searchQuery.toLowerCase()) || conv.jobTitle.toLowerCase().includes(searchQuery.toLowerCase()));

  // Scroll to bottom when new messages are added
  useEffect(() => {
    if (messagesEndRef.current && showConversationDetail) {
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth'
      });
    }
  }, [selectedConversation, showConversationDetail]);

  // Clean up typing timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, []);

  // Handle conversation selection
  const handleSelectConversation = (conversation: Conversation) => {
    setSelectedConversation(conversation);
    setShowConversationDetail(true);
  };

  // Handle sending a new message
  const handleSendMessage = () => {
    if (!messageInput.trim() || !selectedConversation) return;
    toast({
      title: "Message sent",
      description: `Your message has been sent to ${selectedConversation.provider.name}.`
    });

    // Clear input
    setMessageInput('');
  };

  // Handle typing state with debounce
  const handleTypingStart = () => {
    setIsTyping(true);

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing after 1.5 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
    }, 1500);
  };

  // Handle message input change
  const handleMessageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMessageInput(e.target.value);
    handleTypingStart();
  };

  // Handle message input keydown
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Render conversation list
  const renderConversationList = () => <div className={`flex flex-col h-full ${showConversationDetail ? 'hidden' : 'block'}`}>
      <div className="p-4 pb-0">
        <h1 className="text-2xl font-bold mb-2">Messages</h1>
        <p className="text-muted-foreground mb-4 text-sm">Communicate with service providers</p>
      </div>

      {/* Search Input */}
      <div className="relative px-4 mb-4">
        <Input placeholder="Search conversations..." value={searchQuery} onChange={e => setSearchQuery(e.target.value)} className="pl-10" />
        <MessageSquare className="absolute left-8 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-auto px-4 pb-20">
        {filteredConversations.length > 0 ? <div className="space-y-3 animate-fade-in">
            {filteredConversations.map(conversation => <div key={conversation.id} onClick={() => handleSelectConversation(conversation)} className="p-3 rounded-xl border border-gray-100 dark:border-gray-700 shadow-sm hover:border-gray-200 dark:hover:border-gray-600 transition-all active:scale-[0.98] bg-blue-50">
                <div className="flex items-start gap-3">
                  <Avatar>
                    {conversation.provider.avatar && <AvatarImage src={conversation.provider.avatar} alt={conversation.provider.name} />}
                    <AvatarFallback className="bg-gradient-to-br from-blue-400 to-indigo-500 text-white">
                      {conversation.provider.initials}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-center">
                      <h4 className="font-semibold truncate">{conversation.provider.name}</h4>
                      <span className="text-xs text-muted-foreground">{conversation.timestamp}</span>
                    </div>
                    <p className="text-sm text-muted-foreground truncate">{conversation.jobTitle}</p>
                    <p className="text-sm truncate">{conversation.lastMessage}</p>
                  </div>

                  {conversation.unread > 0 && <div className="bg-blue-600 text-white w-5 h-5 rounded-full flex items-center justify-center text-xs">
                      {conversation.unread}
                    </div>}
                </div>
              </div>)}
          </div> : <div className="flex flex-col items-center justify-center h-[calc(100vh-240px)] text-center p-4">
            <div className="w-12 h-12 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mb-4">
              <MessageSquare className="h-6 w-6 text-gray-400" />
            </div>
            <h3 className="font-medium mb-1">No conversations found</h3>
            <p className="text-sm text-muted-foreground">
              {searchQuery ? "Try adjusting your search" : "You don't have any messages yet"}
            </p>
          </div>}
      </div>

      {/* Floating Action Button */}
      <div className="fixed bottom-20 right-4">
        
      </div>
    </div>;

  // Render conversation detail
  const renderConversationDetail = () => {
    if (!selectedConversation) return null;
    return <div className={`flex flex-col h-full ${!showConversationDetail ? 'hidden' : 'block'}`}>
        {/* Header */}
        <div className="sticky top-0 z-10 bg-background border-b p-3 flex items-center gap-2">
          <Button variant="ghost" size="icon" className="flex-none" onClick={() => setShowConversationDetail(false)}>
            <ArrowLeft className="h-5 w-5" />
          </Button>
          
          <Avatar className="h-10 w-10">
            {selectedConversation.provider.avatar && <AvatarImage src={selectedConversation.provider.avatar} alt={selectedConversation.provider.name} />}
            <AvatarFallback className="bg-gradient-to-br from-blue-400 to-indigo-500 text-white">
              {selectedConversation.provider.initials}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 min-w-0">
            <h3 className="font-medium truncate">{selectedConversation.provider.name}</h3>
            <p className="text-xs text-muted-foreground truncate">{selectedConversation.jobTitle}</p>
          </div>
        </div>
        
        {/* Messages */}
        <div className="flex-1 overflow-auto p-4 space-y-3">
          {selectedConversation.messages.map((message, index) => {
          // Group messages by sender
          const isFirstInGroup = index === 0 || selectedConversation.messages[index - 1].isCustomer !== message.isCustomer;
          const isLastInGroup = index === selectedConversation.messages.length - 1 || selectedConversation.messages[index + 1].isCustomer !== message.isCustomer;
          return <div key={message.id} className={`flex ${message.isCustomer ? 'justify-end' : 'justify-start'} ${!isLastInGroup ? 'mb-1' : ''}`}>
                {!message.isCustomer && isFirstInGroup && <Avatar className="h-6 w-6 mr-1 mt-1">
                    {selectedConversation.provider.avatar && <AvatarImage src={selectedConversation.provider.avatar} alt={selectedConversation.provider.name} />}
                    <AvatarFallback className="text-[10px] bg-gradient-to-br from-blue-400 to-indigo-500 text-white">
                      {selectedConversation.provider.initials}
                    </AvatarFallback>
                  </Avatar>}
                
                <div className={`
                    max-w-[80%] px-3 py-2 
                    ${message.isCustomer ? 'bg-blue-600 text-white rounded-t-lg rounded-l-lg' : 'bg-gray-100 dark:bg-gray-800 rounded-t-lg rounded-r-lg'} 
                    ${isFirstInGroup && message.isCustomer ? 'rounded-tr-none' : ''}
                    ${isFirstInGroup && !message.isCustomer ? 'rounded-tl-none' : ''}
                  `}>
                  <p className="text-sm">{message.content}</p>
                  <div className="flex items-center justify-end gap-1 mt-1">
                    <span className="text-xs opacity-70">{message.timestamp}</span>
                    {message.isCustomer && message.status && <span className="text-xs opacity-70">
                        {message.status === 'sent' ? '✓' : message.status === 'delivered' ? '✓✓' : '✓✓'}
                      </span>}
                  </div>
                </div>
              </div>;
        })}
          <div ref={messagesEndRef} />
        </div>
        
        {/* Message Input */}
        <div className="border-t p-3 bg-background">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" className="flex-none">
              <Paperclip className="h-4 w-4" />
            </Button>
            <Input placeholder="Type your message..." value={messageInput} onChange={handleMessageInputChange} onKeyDown={handleKeyDown} className="flex-1" />
            <Button className="flex-none" size="icon" onClick={handleSendMessage} disabled={!messageInput.trim() || isTyping}>
              <Send className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Quick Reply Suggestions */}
          <div className="mt-2 flex gap-2 overflow-x-auto hide-scrollbar pb-1">
            <Button variant="secondary" size="sm" className="whitespace-nowrap text-xs py-1 h-auto" onClick={() => setMessageInput("Yes, that works for me.")}>
              Yes, that works for me
            </Button>
            <Button variant="secondary" size="sm" className="whitespace-nowrap text-xs py-1 h-auto" onClick={() => setMessageInput("What time are you available?")}>
              What time are you available?
            </Button>
            <Button variant="secondary" size="sm" className="whitespace-nowrap text-xs py-1 h-auto" onClick={() => setMessageInput("Thanks for your help!")}>
              Thanks for your help!
            </Button>
          </div>
        </div>
      </div>;
  };
  return <div className="flex flex-col h-[calc(100vh-160px)] overflow-hidden">
      {renderConversationList()}
      {renderConversationDetail()}
    </div>;
}