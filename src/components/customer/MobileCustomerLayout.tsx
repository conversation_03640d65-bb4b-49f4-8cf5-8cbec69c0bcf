
import React from 'react';
import { MobileCustomerHeader } from './MobileCustomerHeader';

interface MobileCustomerLayoutProps {
  children: React.ReactNode;
  title?: string;
}

export function MobileCustomerLayout({ children, title = 'Dashboard' }: MobileCustomerLayoutProps) {
  return (
    <div className="flex flex-col min-h-screen bg-gray-50 dark:bg-gray-900">
      <MobileCustomerHeader title={title} />
      <main className="flex-1 overflow-auto pt-[72px] pb-safe">
        <div className="px-4 py-4 space-y-4 max-w-screen-xl mx-auto">
          {children}
        </div>
      </main>
    </div>
  );
}
