import React from 'react';
import { UniversalChat } from '@/components/chat';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileMessagingInterface } from "./MobileMessagingInterface";
import { ChatProvider } from '@/contexts/ChatContext';
export function CustomerMessages() {
  const { user } = useAuth();
  const isMobile = useIsMobile();

  // If on mobile, render the mobile-optimized component
  if (isMobile) {
    return <MobileMessagingInterface />;
  }

  return (
    <ChatProvider>
      <div className="min-h-screen p-6 bg-blue-50">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-2">
            <div className="p-3 bg-blue-600 rounded-xl shadow-lg">
              <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Messages
              </h1>
              <p className="text-gray-600">Connect with your service providers</p>
            </div>
          </div>
        </div>

        {/* Universal Chat Component */}
        <div className="h-[calc(100vh-12rem)] bg-white rounded-lg shadow-xl border">
          <UniversalChat
            userRole="customer"
            userId={user?.id || 'customer-user'}
            variant="full"
            theme="customer"
            onChatSelect={(chatId) => {
              console.log('Customer selected chat:', chatId);
            }}
            onMessageSent={(message) => {
              console.log('Customer sent message:', message);
            }}
            onError={(error) => {
              console.error('Customer chat error:', error);
            }}
          />
        </div>
      </div>
    </ChatProvider>
  );
}


