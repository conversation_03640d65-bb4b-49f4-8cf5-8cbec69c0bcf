
import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuth } from '@/features/auth/hooks/useAuth';
import {
  Menu,
  Bell,
  LayoutDashboard,
  Briefcase,
  MessageSquare,
  Calendar,
  DollarSign,
  Star,
  TrendingUp,
  Award,
  Users,
  User,
  Settings,
  LogOut,
  FileText
} from 'lucide-react';
import { 
  SidebarProvider, 
  Sidebar, 
  SidebarHeader, 
  SidebarContent, 
  SidebarFooter,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarTrigger,
  SidebarInset
} from '@/components/ui/sidebar';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { cn } from '@/lib/utils';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { NotificationsCenter } from './notifications/NotificationsCenter';
import { SessionNavBar } from '@/components/ui/session-navbar';
import { useToast } from '@/hooks/use-toast';
import { ProviderVerifiedBadge } from './ProviderVerifiedBadge';

// Define sidebar navigation items
const sidebarItems = [
  { 
    name: 'Dashboard', 
    path: '/provider/dashboard', 
    icon: LayoutDashboard 
  },
  {
    name: 'Jobs',
    path: '/provider/jobs',
    icon: Briefcase,
    subItems: [
      { name: 'New Leads', path: '/provider/leads' },
      { name: 'Active Jobs', path: '/provider/jobs/active' },
      { name: 'Completed', path: '/provider/jobs/completed' },
    ]
  },
  {
    name: 'My Bids',
    path: '/provider/bids',
    icon: FileText
  },
  { 
    name: 'Messages', 
    path: '/provider/messages', 
    icon: MessageSquare 
  },
  { 
    name: 'Calendar', 
    path: '/provider/calendar', 
    icon: Calendar 
  },
  { 
    name: 'Earnings', 
    path: '/provider/earnings', 
    icon: DollarSign 
  },
  { 
    name: 'Reviews', 
    path: '/provider/reviews', 
    icon: Star 
  },
  {
    name: 'Performance', 
    path: '/provider/performance', 
    icon: TrendingUp
  },
  {
    name: 'Badges & Rewards', 
    path: '/provider/badges', 
    icon: Award
  },
  {
    name: 'Referrals', 
    path: '/provider/referrals', 
    icon: Users
  },
  { 
    name: 'Profile', 
    path: '/provider/profile', 
    icon: User 
  },
  { 
    name: 'Settings', 
    path: '/provider/settings', 
    icon: Settings 
  },
  {
    name: 'Plans & Pricing', 
    path: '/provider/plans', 
    icon: DollarSign
  },
];

// Mobile sidebar implementation is kept for smaller screens
const MobileSidebar = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { logout } = useAuth();

  const handleLogout = () => {
    logout(); // This will handle token removal and navigation

    toast({
      title: "Logged out successfully",
      description: "You have been logged out of your account.",
    });
  };

  // Modified sidebar items for mobile that doesn't include subitems
  const mobileItems = sidebarItems.map(item => ({
    ...item,
    // Remove subItems for mobile view
    subItems: undefined
  }));

  return (
    <Sheet>
      <SheetTrigger asChild>
        <Button variant="ghost" size="icon" className="md:hidden">
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent side="left" className="w-80 p-0">
        <div className="flex flex-col h-full">
          <div className="p-6 border-b bg-gradient-to-br from-blue-50 to-white dark:from-gray-800 dark:to-gray-900">
            <div className="flex items-center gap-4">
              <Avatar className="h-14 w-14 border-2 border-white shadow-sm">
                <AvatarImage src="/placeholder.svg" alt="Provider" />
                <AvatarFallback>JP</AvatarFallback>
              </Avatar>
              <div className="flex flex-col">
                <div className="flex items-center gap-1.5">
                  <span className="font-medium text-lg">John's Plumbing</span>
                  <ProviderVerifiedBadge plan="pro" size="sm" />
                </div>
                <span className="text-base text-muted-foreground">Gold Provider</span>
              </div>
            </div>
          </div>
          <div className="flex-1 overflow-auto py-4">
            <nav className="grid gap-2 px-3">
              {mobileItems.map((item) => (
                <Button
                  key={item.name}
                  variant={location.pathname === item.path ? "secondary" : "ghost"}
                  className={cn(
                    "justify-start text-base py-7 px-4 font-medium rounded-lg",
                    location.pathname === item.path ? "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300" : ""
                  )}
                  onClick={() => navigate(item.path)}
                >
                  <item.icon className="mr-3 h-5 w-5" />
                  {item.name}
                </Button>
              ))}
            </nav>
          </div>
          <div className="p-6 border-t bg-gradient-to-br from-white to-blue-50 dark:from-gray-900 dark:to-gray-800">
            <Button variant="outline" className="w-full py-6 text-base gap-3 font-medium rounded-lg shadow-sm hover:shadow" onClick={handleLogout}>
              <LogOut className="h-5 w-5" />
              <span>Sign Out</span>
            </Button>
          </div>
        </div>
      </SheetContent>
    </Sheet>
  );
};

interface ProviderDashboardLayoutProps {
  children: React.ReactNode;
  pageTitle?: string;
}

export const ProviderDashboardLayout = ({ 
  children, 
  pageTitle 
}: ProviderDashboardLayoutProps) => {
  const isMobile = useIsMobile();

  return (
    <div className={cn(
      "flex-1 flex flex-col min-h-screen",
      !isMobile && "ml-[3.05rem]" // Default collapsed width
    )}>
      {!isMobile && <SessionNavBar />}
      <header className="border-b p-4 flex items-center justify-between bg-white dark:bg-gray-900 sticky top-0 z-30 shadow-sm h-16">
        <div className="flex items-center gap-3">
          {isMobile && <MobileSidebar />}
          <h1 className={`${isMobile ? 'text-lg' : 'text-xl'} font-semibold`}>{pageTitle}</h1>
        </div>
        <div className="flex items-center gap-2">
          <NotificationsCenter />
          {isMobile && (
            <Avatar className="h-8 w-8">
              <AvatarImage src="/placeholder.svg" alt="Provider" />
              <AvatarFallback>JP</AvatarFallback>
            </Avatar>
          )}
        </div>
      </header>
      <main className={`p-4 md:p-6 bg-gray-50 dark:bg-gray-900 flex-1 overflow-auto ${
        isMobile
          ? 'pt-4 pb-24 min-h-[calc(100vh-64px-80px)]' // Account for header (64px) + mobile footer (80px)
          : 'pt-4 pb-20 min-h-[calc(100vh-64px-64px)]'  // Account for header (64px) + desktop footer (64px)
      }`}>
        {children}
      </main>
    </div>
  );
};
