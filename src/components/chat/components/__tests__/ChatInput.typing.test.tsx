import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ChatInput } from '../ChatInput';
import { useChat } from '@/hooks/useChat';
import { useToast } from '@/hooks/use-toast';

// Mock dependencies
jest.mock('@/hooks/useChat');
jest.mock('@/hooks/use-toast');

const mockUseChat = useChat as jest.MockedFunction<typeof useChat>;
const mockUseToast = useToast as jest.MockedFunction<typeof useToast>;

describe('ChatInput Typing State', () => {
  const mockSendMessage = jest.fn();
  const mockToast = jest.fn();

  beforeEach(() => {
    mockUseChat.mockReturnValue({
      sendMessage: mockSendMessage,
      state: { isLoading: false },
      dispatch: jest.fn(),
      initializeForRole: jest.fn(),
    } as any);

    mockUseToast.mockReturnValue({
      toast: mockToast,
    });

    jest.clearAllMocks();
  });

  const defaultProps = {
    chatId: 'test-chat-id',
    userRole: 'customer' as const,
    features: {
      hasFileUpload: false,
      hasVoiceMessages: false,
      hasEmojiPicker: false,
      hasTypingIndicator: false,
    },
    theme: {
      primary: '#000',
      secondary: '#666',
      background: '#fff',
      text: '#000',
      textSecondary: '#666',
      border: '#ddd',
      inputBackground: '#f9f9f9',
    },
  };

  it('should disable send button while user is typing', async () => {
    const user = userEvent.setup();
    
    render(<ChatInput {...defaultProps} />);
    
    const textarea = screen.getByPlaceholderText('Type a message...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    // Initially, send button should be disabled (no message)
    expect(sendButton).toBeDisabled();
    
    // Start typing
    await user.type(textarea, 'Hello');
    
    // Send button should be disabled while typing
    expect(sendButton).toBeDisabled();
    
    // Wait for typing timeout (1.5 seconds)
    await waitFor(() => {
      expect(sendButton).not.toBeDisabled();
    }, { timeout: 2000 });
  });

  it('should reset typing state when user stops typing', async () => {
    const user = userEvent.setup();
    
    render(<ChatInput {...defaultProps} />);
    
    const textarea = screen.getByPlaceholderText('Type a message...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    // Type a message
    await user.type(textarea, 'Hello world');
    
    // Button should be disabled while typing
    expect(sendButton).toBeDisabled();
    
    // Wait for typing to stop
    await waitFor(() => {
      expect(sendButton).not.toBeDisabled();
    }, { timeout: 2000 });
    
    // Should be able to send message now
    await user.click(sendButton);
    expect(mockSendMessage).toHaveBeenCalledWith('test-chat-id', {
      type: 'text',
      message: 'Hello world',
    });
  });

  it('should extend typing timeout when user continues typing', async () => {
    const user = userEvent.setup();
    
    render(<ChatInput {...defaultProps} />);
    
    const textarea = screen.getByPlaceholderText('Type a message...');
    const sendButton = screen.getByRole('button', { name: /send/i });
    
    // Start typing
    await user.type(textarea, 'Hello');
    expect(sendButton).toBeDisabled();
    
    // Continue typing after 1 second
    setTimeout(async () => {
      await user.type(textarea, ' world');
    }, 1000);
    
    // Button should still be disabled after 1.2 seconds (typing continued)
    await new Promise(resolve => setTimeout(resolve, 1200));
    expect(sendButton).toBeDisabled();
    
    // Should be enabled after full timeout
    await waitFor(() => {
      expect(sendButton).not.toBeDisabled();
    }, { timeout: 2000 });
  });
});
