import React, { useState } from 'react';
import { Chat, ChatSettings as ChatSettingsType } from '@/types/chat';
import { ChatFeatures, ChatTheme } from '../config/roleConfigurations';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  X, 
  Bell, 
  BellOff, 
  Volume2, 
  VolumeX, 
  Palette, 
  Type, 
  Clock,
  Download,
  Trash2,
  AlertTriangle
} from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { cn } from '@/lib/utils';

interface ChatSettingsProps {
  chat: Chat | null;
  userRole: 'admin' | 'provider' | 'customer';
  features: ChatFeatures;
  theme: ChatTheme;
  onClose: () => void;
  onSettingsChange?: (settings: Partial<ChatSettingsType>) => void;
  onDeleteChat?: () => void;
  onLeaveChat?: () => void;
  onClearHistory?: () => void;
}

/**
 * ChatSettings Component
 * 
 * Provides chat-specific settings and preferences with role-based options.
 * Includes notification settings, appearance options, and chat management actions.
 */
export const ChatSettings: React.FC<ChatSettingsProps> = ({
  chat,
  userRole,
  features,
  theme,
  onClose,
  onSettingsChange,
  onDeleteChat,
  onLeaveChat,
  onClearHistory,
}) => {
  // Default settings
  const [settings, setSettings] = useState<ChatSettingsType>({
    isMuted: false,
    notifications: true,
    theme: 'auto',
    fontSize: 'medium',
    soundEnabled: true,
    enterToSend: true,
    showTimestamps: true,
    showReadReceipts: features.hasReadReceipts,
    autoDownloadMedia: true,
  });

  // Handle setting change
  const handleSettingChange = <K extends keyof ChatSettingsType>(
    key: K,
    value: ChatSettingsType[K]
  ) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    
    if (onSettingsChange) {
      onSettingsChange({ [key]: value });
    }
  };

  if (!chat) {
    return (
      <div className="w-80 border-l border-[var(--chat-border)] bg-[var(--chat-background)] p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Settings</h3>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="text-center text-[var(--chat-text-secondary)]">
          <p>No chat selected</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-80 border-l border-[var(--chat-border)] bg-[var(--chat-background)] flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-[var(--chat-border)]">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-[var(--chat-text)]">
            Chat Settings
          </h3>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <ScrollArea className="flex-1">
        <div className="p-4 space-y-6">
          {/* Notification Settings */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-[var(--chat-text)] flex items-center">
              <Bell className="h-4 w-4 mr-2" />
              Notifications
            </h4>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="notifications" className="text-sm">
                  Enable notifications
                </Label>
                <Switch
                  id="notifications"
                  checked={settings.notifications}
                  onCheckedChange={(checked) => handleSettingChange('notifications', checked)}
                />
              </div>
              
              {features.canMuteConversations && (
                <div className="flex items-center justify-between">
                  <Label htmlFor="muted" className="text-sm">
                    Mute this chat
                  </Label>
                  <Switch
                    id="muted"
                    checked={settings.isMuted}
                    onCheckedChange={(checked) => handleSettingChange('isMuted', checked)}
                  />
                </div>
              )}
              
              <div className="flex items-center justify-between">
                <Label htmlFor="sound" className="text-sm">
                  Sound notifications
                </Label>
                <Switch
                  id="sound"
                  checked={settings.soundEnabled}
                  onCheckedChange={(checked) => handleSettingChange('soundEnabled', checked)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Appearance Settings */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-[var(--chat-text)] flex items-center">
              <Palette className="h-4 w-4 mr-2" />
              Appearance
            </h4>
            
            <div className="space-y-3">
              <div className="space-y-2">
                <Label className="text-sm">Theme</Label>
                <Select
                  value={settings.theme}
                  onValueChange={(value: 'light' | 'dark' | 'auto') => 
                    handleSettingChange('theme', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="auto">Auto</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label className="text-sm">Font size</Label>
                <Select
                  value={settings.fontSize}
                  onValueChange={(value: 'small' | 'medium' | 'large') => 
                    handleSettingChange('fontSize', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="small">Small</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="large">Large</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          <Separator />

          {/* Message Settings */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-[var(--chat-text)] flex items-center">
              <Type className="h-4 w-4 mr-2" />
              Messages
            </h4>
            
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="enter-send" className="text-sm">
                  Press Enter to send
                </Label>
                <Switch
                  id="enter-send"
                  checked={settings.enterToSend}
                  onCheckedChange={(checked) => handleSettingChange('enterToSend', checked)}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="timestamps" className="text-sm">
                  Show timestamps
                </Label>
                <Switch
                  id="timestamps"
                  checked={settings.showTimestamps}
                  onCheckedChange={(checked) => handleSettingChange('showTimestamps', checked)}
                />
              </div>
              
              {features.hasReadReceipts && (
                <div className="flex items-center justify-between">
                  <Label htmlFor="read-receipts" className="text-sm">
                    Show read receipts
                  </Label>
                  <Switch
                    id="read-receipts"
                    checked={settings.showReadReceipts}
                    onCheckedChange={(checked) => handleSettingChange('showReadReceipts', checked)}
                  />
                </div>
              )}
              
              <div className="flex items-center justify-between">
                <Label htmlFor="auto-download" className="text-sm">
                  Auto-download media
                </Label>
                <Switch
                  id="auto-download"
                  checked={settings.autoDownloadMedia}
                  onCheckedChange={(checked) => handleSettingChange('autoDownloadMedia', checked)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Chat Actions */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-[var(--chat-text)]">
              Chat Actions
            </h4>
            
            <div className="space-y-2">
              {features.canAccessHistory && (
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => {
                    // TODO: Export chat history
                    console.log('Export chat history');
                  }}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export Chat History
                </Button>
              )}
              
              {(userRole === 'admin' || features.canDeleteMessages) && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start text-orange-600 hover:text-orange-700"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Clear Chat History
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Clear Chat History</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will permanently delete all messages in this chat. This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={onClearHistory}
                        className="bg-orange-600 hover:bg-orange-700"
                      >
                        Clear History
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </div>
          </div>

          <Separator />

          {/* Danger Zone */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-red-600 flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2" />
              Danger Zone
            </h4>
            
            <div className="space-y-2">
              {chat.type === 'group' && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                    >
                      Leave Chat
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Leave Chat</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to leave this chat? You won't receive any new messages.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={onLeaveChat}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        Leave Chat
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
              
              {userRole === 'admin' && (
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full justify-start text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
                    >
                      Delete Chat
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Chat</AlertDialogTitle>
                      <AlertDialogDescription>
                        This will permanently delete this chat and all its messages for all participants. This action cannot be undone.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={onDeleteChat}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        Delete Chat
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </div>
          </div>
        </div>
      </ScrollArea>
    </div>
  );
};

export default ChatSettings;
