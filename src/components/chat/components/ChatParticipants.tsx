import React, { useState } from 'react';
import { Chat } from '@/types/chat';
import { ChatFeatures, ChatTheme } from '../config/roleConfigurations';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  X, 
  Plus, 
  Search, 
  MoreVertical, 
  UserMinus, 
  Crown,
  Shield,
  User
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { cn } from '@/lib/utils';

interface ChatParticipantsProps {
  chat: Chat | null;
  userRole: 'admin' | 'provider' | 'customer';
  features: ChatFeatures;
  theme: ChatTheme;
  onClose: () => void;
  onAddParticipant?: (userId: string) => void;
  onRemoveParticipant?: (userId: string) => void;
  onPromoteParticipant?: (userId: string) => void;
}

/**
 * ChatParticipants Component
 * 
 * Displays and manages chat participants with role-based permissions.
 * Allows adding/removing participants, promoting users, and viewing participant details.
 */
export const ChatParticipants: React.FC<ChatParticipantsProps> = ({
  chat,
  userRole,
  features,
  theme,
  onClose,
  onAddParticipant,
  onRemoveParticipant,
  onPromoteParticipant,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddParticipant, setShowAddParticipant] = useState(false);

  if (!chat) {
    return (
      <div className="w-80 border-l border-[var(--chat-border)] bg-[var(--chat-background)] p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold">Participants</h3>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="text-center text-[var(--chat-text-secondary)]">
          <p>No chat selected</p>
        </div>
      </div>
    );
  }

  // Filter participants based on search
  const filteredParticipants = chat.participants.filter(participant =>
    participant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    participant.email?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Get participant role icon
  const getParticipantRoleIcon = (participantType: string) => {
    switch (participantType) {
      case 'admin':
        return <Crown className="h-3 w-3 text-yellow-500" />;
      case 'provider':
        return <Shield className="h-3 w-3 text-blue-500" />;
      case 'customer':
        return <User className="h-3 w-3 text-gray-500" />;
      default:
        return <User className="h-3 w-3 text-gray-500" />;
    }
  };

  // Check if user can manage participant
  const canManageParticipant = (participantId: string, participantType: string) => {
    if (!features.canManageParticipants) return false;
    if (userRole !== 'admin') return false;
    if (participantType === 'admin' && userRole !== 'admin') return false;
    return true;
  };

  // Render participant actions
  const renderParticipantActions = (participant: any) => {
    if (!canManageParticipant(participant.id, participant.type)) {
      return null;
    }

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
            <MoreVertical className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-40">
          <DropdownMenuItem>
            View Profile
          </DropdownMenuItem>
          
          <DropdownMenuItem>
            Send Message
          </DropdownMenuItem>
          
          {participant.type !== 'admin' && userRole === 'admin' && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => onPromoteParticipant?.(participant.id)}>
                Promote to Admin
              </DropdownMenuItem>
            </>
          )}
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem 
            className="text-red-600"
            onClick={() => onRemoveParticipant?.(participant.id)}
          >
            <UserMinus className="h-3 w-3 mr-2" />
            Remove from Chat
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    );
  };

  // Render individual participant
  const renderParticipant = (participant: any) => {
    return (
      <div
        key={participant.id}
        className="flex items-center space-x-3 p-3 hover:bg-[var(--chat-surface)] rounded-lg transition-colors"
      >
        {/* Avatar */}
        <div className="relative flex-shrink-0">
          <Avatar className="w-10 h-10">
            <AvatarImage src={participant.avatar} />
            <AvatarFallback>
              {participant.name.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          
          {/* Online indicator */}
          {features.canSeeOnlineStatus && participant.is_online && (
            <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
          )}
        </div>

        {/* Participant info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            <h4 className="text-sm font-medium text-[var(--chat-text)] truncate">
              {participant.name}
            </h4>
            {getParticipantRoleIcon(participant.type)}
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              {participant.type}
            </Badge>
            
            {features.canSeeOnlineStatus && (
              <span className="text-xs text-[var(--chat-text-secondary)]">
                {participant.is_online ? 'Online' : 
                 participant.last_seen ? `Last seen ${participant.last_seen}` : 'Offline'}
              </span>
            )}
          </div>
          
          {participant.email && (
            <p className="text-xs text-[var(--chat-text-secondary)] truncate mt-1">
              {participant.email}
            </p>
          )}
        </div>

        {/* Actions */}
        <div className="flex-shrink-0">
          {renderParticipantActions(participant)}
        </div>
      </div>
    );
  };

  // Render add participant section
  const renderAddParticipant = () => {
    if (!features.canManageParticipants || !showAddParticipant) {
      return null;
    }

    return (
      <div className="p-3 border-t border-[var(--chat-border)]">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium">Add Participant</h4>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={() => setShowAddParticipant(false)}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
          
          <Input
            placeholder="Search users to add..."
            className="text-sm"
          />
          
          {/* TODO: Add user search results */}
          <div className="text-xs text-[var(--chat-text-secondary)]">
            Search for users to add to this conversation
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="w-80 border-l border-[var(--chat-border)] bg-[var(--chat-background)] flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-[var(--chat-border)]">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-lg font-semibold text-[var(--chat-text)]">
            Participants ({chat.participants.length})
          </h3>
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-[var(--chat-text-secondary)]" />
          <Input
            placeholder="Search participants..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 text-sm bg-[var(--chat-surface)] border-[var(--chat-border)]"
          />
        </div>
        
        {/* Add participant button */}
        {features.canManageParticipants && chat.type === 'group' && (
          <Button
            variant="outline"
            size="sm"
            className="w-full"
            onClick={() => setShowAddParticipant(true)}
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Participant
          </Button>
        )}
      </div>

      {/* Participants list */}
      <ScrollArea className="flex-1">
        <div className="space-y-1 p-2">
          {filteredParticipants.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-sm text-[var(--chat-text-secondary)]">
                {searchQuery ? 'No participants found' : 'No participants'}
              </p>
            </div>
          ) : (
            filteredParticipants.map(renderParticipant)
          )}
        </div>
      </ScrollArea>

      {/* Add participant section */}
      {renderAddParticipant()}

      {/* Chat info */}
      <div className="p-4 border-t border-[var(--chat-border)] bg-[var(--chat-surface)]">
        <div className="space-y-2 text-xs text-[var(--chat-text-secondary)]">
          <div className="flex justify-between">
            <span>Chat Type:</span>
            <span className="capitalize">{chat.type}</span>
          </div>
          
          <div className="flex justify-between">
            <span>Created:</span>
            <span>{new Date(chat.created_at).toLocaleDateString()}</span>
          </div>
          
          {chat.description && (
            <div>
              <span className="block mb-1">Description:</span>
              <p className="text-[var(--chat-text)] text-sm">{chat.description}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ChatParticipants;
