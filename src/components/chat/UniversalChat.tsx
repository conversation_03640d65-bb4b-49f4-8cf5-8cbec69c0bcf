import React, { useEffect, useMemo } from 'react';
import { UniversalChatProps } from '@/types/chat';
import { useChat } from '@/hooks/useChat';
import { 
  getChatFeatures, 
  getChatTheme, 
  getChatVariantConfig,
  UserRole 
} from './config/roleConfigurations';
import { ChatContainer } from './components/ChatContainer';
import { cn } from '@/lib/utils';

/**
 * UniversalChat Component
 * 
 * A reusable chat component that adapts to different user roles (admin, provider, customer)
 * with role-specific features, permissions, and theming.
 * 
 * Features:
 * - Role-based permissions and features
 * - Multiple variants (compact, full, modal)
 * - Customizable themes
 * - Real-time messaging with WebSocket support
 * - File sharing and media support
 * - Responsive design
 */
export const UniversalChat: React.FC<UniversalChatProps> = ({
  userRole,
  userId,
  recipientId,
  chatId,
  variant = 'full',
  features: customFeatures,
  theme = 'default',
  customTheme,
  className,
  onChatSelect,
  onMessageSent,
  onError,
}) => {
  const { state, dispatch, initializeForRole } = useChat();

  // Get role-based configuration
  const roleFeatures = useMemo(() => getChatFeatures(userRole), [userRole]);
  const roleTheme = useMemo(() => getChatTheme(userRole), [userRole]);
  const variantConfig = useMemo(() => getChatVariantConfig(variant), [variant]);

  // Merge custom features with role-based features
  const finalFeatures = useMemo(() => ({
    ...roleFeatures,
    ...customFeatures,
  }), [roleFeatures, customFeatures]);

  // Determine final theme
  const finalTheme = useMemo(() => {
    let baseTheme = roleTheme;
    
    if (theme !== 'default') {
      switch (theme) {
        case 'admin':
          baseTheme = getChatTheme('admin');
          break;
        case 'provider':
          baseTheme = getChatTheme('provider');
          break;
        case 'customer':
          baseTheme = getChatTheme('customer');
          break;
      }
    }
    
    return {
      ...baseTheme,
      ...customTheme,
    };
  }, [roleTheme, theme, customTheme]);

  // Initialize chat for the specific role
  useEffect(() => {
    if (userId && userRole) {
      const currentUser = {
        id: userId,
        name: '', // This should be populated from user context
        type: userRole as 'admin' | 'provider' | 'customer',
      };
      
      initializeForRole(userRole, currentUser);
    }
  }, [userId, userRole, initializeForRole]);

  // Handle errors
  useEffect(() => {
    if (state.error && onError) {
      onError(state.error);
    }
  }, [state.error, onError]);

  // Generate CSS custom properties for theming
  const themeStyles = useMemo(() => ({
    '--chat-primary': finalTheme.primary,
    '--chat-secondary': finalTheme.secondary,
    '--chat-accent': finalTheme.accent,
    '--chat-background': finalTheme.background,
    '--chat-surface': finalTheme.surface,
    '--chat-text': finalTheme.text,
    '--chat-text-secondary': finalTheme.textSecondary,
    '--chat-border': finalTheme.border,
    '--chat-success': finalTheme.success,
    '--chat-warning': finalTheme.warning,
    '--chat-error': finalTheme.error,
  } as React.CSSProperties), [finalTheme]);

  // Generate variant styles
  const variantStyles = useMemo(() => ({
    height: variantConfig.height,
    width: variantConfig.width,
    borderRadius: variantConfig.borderRadius,
    boxShadow: variantConfig.shadow,
  } as React.CSSProperties), [variantConfig]);

  // Combine all styles
  const containerStyles = useMemo(() => ({
    ...themeStyles,
    ...variantStyles,
  }), [themeStyles, variantStyles]);

  // Handle chat selection
  const handleChatSelect = (selectedChatId: string) => {
    dispatch({ type: 'SELECT_CHAT', payload: selectedChatId });
    if (onChatSelect) {
      onChatSelect(selectedChatId);
    }
  };

  // Handle message sent
  const handleMessageSent = (message: any) => {
    if (onMessageSent) {
      onMessageSent(message);
    }
  };

  return (
    <div
      className={cn(
        'universal-chat',
        `universal-chat--${variant}`,
        `universal-chat--${userRole}`,
        'bg-[var(--chat-background)]',
        'border border-[var(--chat-border)]',
        'text-[var(--chat-text)]',
        'overflow-hidden',
        'flex flex-col',
        {
          'relative': variant === 'full',
          'fixed inset-0 z-50': variant === 'modal',
          'rounded-lg': variant !== 'full',
        },
        className
      )}
      style={containerStyles}
      data-testid="universal-chat"
      data-role={userRole}
      data-variant={variant}
    >
      <ChatContainer
        userRole={userRole}
        userId={userId}
        recipientId={recipientId}
        chatId={chatId}
        variant={variant}
        features={finalFeatures}
        theme={finalTheme}
        variantConfig={variantConfig}
        onChatSelect={handleChatSelect}
        onMessageSent={handleMessageSent}
      />
    </div>
  );
};

// Export types for external use
export type { UniversalChatProps };

// Default export
export default UniversalChat;
