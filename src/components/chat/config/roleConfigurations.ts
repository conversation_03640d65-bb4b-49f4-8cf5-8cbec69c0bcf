// Role-based configuration for universal chat component
// This file defines the features and permissions available to each user role

export type UserRole = 'admin' | 'provider' | 'customer';

export interface ChatFeatures {
  canCreateGroups: boolean;
  canManageParticipants: boolean;
  canAccessHistory: boolean;
  hasFileUpload: boolean;
  hasVoiceMessages: boolean;
  hasVideoCall: boolean;
  hasTypingIndicators: boolean;
  hasReadReceipts: boolean;
  canDeleteMessages: boolean;
  canEditMessages: boolean;
  canPinMessages: boolean;
  canMuteConversations: boolean;
  canBlockUsers: boolean;
  canReportMessages: boolean;
  maxFileSize: number; // in MB
  allowedFileTypes: string[];
  maxMessageLength: number;
  canSeeOnlineStatus: boolean;
  canInitiateChat: boolean;
}

export interface ChatTheme {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  warning: string;
  error: string;
}

export interface ChatVariantConfig {
  showHeader: boolean;
  showParticipants: boolean;
  showSettings: boolean;
  height: string;
  width: string;
  borderRadius: string;
  shadow: string;
}

// Role-based feature configurations
export const CHAT_ROLE_CONFIGS: Record<UserRole, ChatFeatures> = {
  admin: {
    canCreateGroups: true,
    canManageParticipants: true,
    canAccessHistory: true,
    hasFileUpload: true,
    hasVoiceMessages: true,
    hasVideoCall: true,
    hasTypingIndicators: true,
    hasReadReceipts: true,
    canDeleteMessages: true,
    canEditMessages: true,
    canPinMessages: true,
    canMuteConversations: true,
    canBlockUsers: true,
    canReportMessages: false, // Admins don't report, they handle reports
    maxFileSize: 50, // 50MB for admins
    allowedFileTypes: ['*'], // All file types
    maxMessageLength: 5000,
    canSeeOnlineStatus: true,
    canInitiateChat: true,
  },
  provider: {
    canCreateGroups: false,
    canManageParticipants: false,
    canAccessHistory: true,
    hasFileUpload: true,
    hasVoiceMessages: true,
    hasVideoCall: false, // Video calls might be premium feature
    hasTypingIndicators: true,
    hasReadReceipts: true,
    canDeleteMessages: true, // Only their own messages
    canEditMessages: true, // Only their own messages
    canPinMessages: false,
    canMuteConversations: true,
    canBlockUsers: true,
    canReportMessages: true,
    maxFileSize: 25, // 25MB for providers
    allowedFileTypes: ['image/*', 'application/pdf', 'text/*', 'video/*'],
    maxMessageLength: 2000,
    canSeeOnlineStatus: true,
    canInitiateChat: true,
  },
  customer: {
    canCreateGroups: false,
    canManageParticipants: false,
    canAccessHistory: true,
    hasFileUpload: true,
    hasVoiceMessages: false, // Voice messages might be premium
    hasVideoCall: false,
    hasTypingIndicators: true,
    hasReadReceipts: false, // Privacy consideration for customers
    canDeleteMessages: true, // Only their own messages
    canEditMessages: false, // Customers can't edit for transparency
    canPinMessages: false,
    canMuteConversations: true,
    canBlockUsers: true,
    canReportMessages: true,
    maxFileSize: 10, // 10MB for customers
    allowedFileTypes: ['image/*', 'application/pdf', 'text/*'],
    maxMessageLength: 1000,
    canSeeOnlineStatus: false, // Privacy consideration
    canInitiateChat: true,
  },
};

// Theme configurations for each role - Updated to use schema colors
export const CHAT_THEME_CONFIGS: Record<UserRole, ChatTheme> = {
  admin: {
    primary: '#2563EB', // Use schema primary color
    secondary: '#64748b',
    accent: '#f59e0b',
    background: 'hsl(var(--background))', // Use schema background
    surface: 'hsl(var(--card))', // Use schema card background
    text: 'hsl(var(--foreground))', // Use schema text color
    textSecondary: 'hsl(var(--muted-foreground))', // Use schema muted text
    border: 'hsl(var(--border))', // Use schema border color
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
  },
  provider: {
    primary: '#2563EB', // Use schema primary color for consistency
    secondary: '#64748b',
    accent: '#059669', // Keep green as accent for providers
    background: 'hsl(var(--background))', // Use schema background
    surface: 'hsl(var(--card))', // Use schema card background
    text: 'hsl(var(--foreground))', // Use schema text color
    textSecondary: 'hsl(var(--muted-foreground))', // Use schema muted text
    border: 'hsl(var(--border))', // Use schema border color
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
  },
  customer: {
    primary: '#2563EB', // Use schema primary color for consistency
    secondary: '#64748b',
    accent: '#7c3aed', // Keep purple as accent for customers
    background: 'hsl(var(--background))', // Use schema background
    surface: 'hsl(var(--card))', // Use schema card background
    text: 'hsl(var(--foreground))', // Use schema text color
    textSecondary: 'hsl(var(--muted-foreground))', // Use schema muted text
    border: 'hsl(var(--border))', // Use schema border color
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
  },
};

// Variant configurations
export const CHAT_VARIANT_CONFIGS: Record<'compact' | 'full' | 'modal', ChatVariantConfig> = {
  compact: {
    showHeader: true,
    showParticipants: false,
    showSettings: false,
    height: '400px',
    width: '320px',
    borderRadius: '12px',
    shadow: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
  },
  full: {
    showHeader: true,
    showParticipants: true,
    showSettings: true,
    height: '100%',
    width: '100%',
    borderRadius: '0px',
    shadow: 'none',
  },
  modal: {
    showHeader: true,
    showParticipants: false,
    showSettings: true,
    height: '600px',
    width: '800px',
    borderRadius: '16px',
    shadow: '0 25px 50px -12px rgb(0 0 0 / 0.25)',
  },
};

// Helper functions
export function getChatFeatures(userRole: UserRole): ChatFeatures {
  return CHAT_ROLE_CONFIGS[userRole];
}

export function getChatTheme(userRole: UserRole): ChatTheme {
  return CHAT_THEME_CONFIGS[userRole];
}

export function getChatVariantConfig(variant: 'compact' | 'full' | 'modal'): ChatVariantConfig {
  return CHAT_VARIANT_CONFIGS[variant];
}

// Permission checker functions
export function canUserPerformAction(
  userRole: UserRole, 
  action: keyof ChatFeatures
): boolean {
  const features = getChatFeatures(userRole);
  return features[action] as boolean;
}

export function isFileAllowed(
  userRole: UserRole, 
  fileType: string, 
  fileSize: number
): { allowed: boolean; reason?: string } {
  const features = getChatFeatures(userRole);
  
  // Check file size
  if (fileSize > features.maxFileSize * 1024 * 1024) {
    return {
      allowed: false,
      reason: `File size exceeds ${features.maxFileSize}MB limit`,
    };
  }
  
  // Check file type
  if (!features.allowedFileTypes.includes('*')) {
    const isAllowed = features.allowedFileTypes.some(allowedType => {
      if (allowedType.endsWith('/*')) {
        const category = allowedType.replace('/*', '');
        return fileType.startsWith(category);
      }
      return fileType === allowedType;
    });
    
    if (!isAllowed) {
      return {
        allowed: false,
        reason: 'File type not allowed',
      };
    }
  }
  
  return { allowed: true };
}

export function getMaxMessageLength(userRole: UserRole): number {
  return getChatFeatures(userRole).maxMessageLength;
}
