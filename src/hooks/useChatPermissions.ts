import { useMemo } from 'react';
import { 
  getChatFeatures, 
  getChatTheme, 
  canUserPerformAction, 
  isFileAllowed,
  getMaxMessageLength,
  UserRole,
  ChatFeatures,
  ChatTheme 
} from '@/components/chat/config/roleConfigurations';

/**
 * Hook for managing chat permissions based on user role
 */
export function useChatPermissions(userRole: UserRole) {
  const permissions = useMemo(() => getChatFeatures(userRole), [userRole]);
  
  const canPerformAction = useMemo(() => 
    (action: keyof ChatFeatures) => canUserPerformAction(userRole, action),
    [userRole]
  );
  
  const checkFilePermission = useMemo(() => 
    (fileType: string, fileSize: number) => isFileAllowed(userRole, fileType, fileSize),
    [userRole]
  );
  
  const maxMessageLength = useMemo(() => getMaxMessageLength(userRole), [userRole]);
  
  return {
    permissions,
    canPerformAction,
    checkFilePermission,
    maxMessageLength,
  };
}

/**
 * Hook for managing chat features based on user role and custom overrides
 */
export function useChatFeatures(
  userRole: UserRole, 
  customFeatures?: Partial<ChatFeatures>
) {
  const baseFeatures = useMemo(() => getChatFeatures(userRole), [userRole]);
  
  const features = useMemo(() => ({
    ...baseFeatures,
    ...customFeatures,
  }), [baseFeatures, customFeatures]);
  
  return features;
}

/**
 * Hook for managing chat theme based on user role and custom overrides
 */
export function useChatTheme(
  userRole: UserRole,
  themeOverride?: 'default' | 'admin' | 'provider' | 'customer' | 'custom',
  customTheme?: Partial<ChatTheme>
) {
  const theme = useMemo(() => {
    let baseTheme = getChatTheme(userRole);
    
    if (themeOverride && themeOverride !== 'default' && themeOverride !== 'custom') {
      baseTheme = getChatTheme(themeOverride);
    }
    
    return {
      ...baseTheme,
      ...customTheme,
    };
  }, [userRole, themeOverride, customTheme]);
  
  return theme;
}

/**
 * Hook for checking specific chat permissions
 */
export function useSpecificChatPermissions(userRole: UserRole) {
  const permissions = useChatPermissions(userRole);
  
  return {
    // Message permissions
    canSendMessages: true, // All roles can send messages
    canEditMessages: permissions.canPerformAction('canEditMessages'),
    canDeleteMessages: permissions.canPerformAction('canDeleteMessages'),
    canPinMessages: permissions.canPerformAction('canPinMessages'),
    canReportMessages: permissions.canPerformAction('canReportMessages'),
    
    // Chat management permissions
    canCreateGroups: permissions.canPerformAction('canCreateGroups'),
    canManageParticipants: permissions.canPerformAction('canManageParticipants'),
    canMuteConversations: permissions.canPerformAction('canMuteConversations'),
    canBlockUsers: permissions.canPerformAction('canBlockUsers'),
    
    // Media permissions
    canUploadFiles: permissions.canPerformAction('hasFileUpload'),
    canSendVoiceMessages: permissions.canPerformAction('hasVoiceMessages'),
    canMakeVideoCalls: permissions.canPerformAction('hasVideoCall'),
    
    // Privacy permissions
    canSeeOnlineStatus: permissions.canPerformAction('canSeeOnlineStatus'),
    canSeeReadReceipts: permissions.canPerformAction('hasReadReceipts'),
    canSeeTypingIndicators: permissions.canPerformAction('hasTypingIndicators'),
    
    // Access permissions
    canAccessHistory: permissions.canPerformAction('canAccessHistory'),
    canInitiateChat: permissions.canPerformAction('canInitiateChat'),
    
    // File validation
    validateFile: permissions.checkFilePermission,
    maxMessageLength: permissions.maxMessageLength,
  };
}
