import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/features/auth/hooks/useAuth';
import { chatService } from '@/services/chatService';
import { Chat, Message, SendMessageRequest } from '@/types/chat';
import { useChat } from '@/hooks/useChat';

/**
 * TanStack Query hooks for chat functionality
 * 
 * These hooks provide optimized data fetching, caching, and real-time updates
 * for the universal chat system with role-based permissions.
 */

// Query keys for consistent cache management
export const chatQueryKeys = {
  all: ['chats'] as const,
  lists: () => [...chatQueryKeys.all, 'list'] as const,
  list: (userId: string, userRole: string) => [...chatQueryKeys.lists(), userId, userRole] as const,
  details: () => [...chatQueryKeys.all, 'detail'] as const,
  detail: (chatId: string) => [...chatQueryKeys.details(), chatId] as const,
  messages: (chatId: string) => [...chatQueryKeys.detail(chatId), 'messages'] as const,
  messagesPage: (chatId: string, page: number) => [...chatQueryKeys.messages(chatId), page] as const,
};

/**
 * Hook to fetch user's chat list with role-based filtering
 */
export function useChatList(userId: string, userRole: string) {
  const { token } = useAuth();
  
  return useQuery({
    queryKey: chatQueryKeys.list(userId, userRole),
    queryFn: async () => {
      const response = await chatService.getChats(token || undefined);
      if (!response.isSuccess || !response.data) {
        throw new Error(response.error || 'Failed to fetch chats');
      }
      return response.data.data;
    },
    enabled: !!userId && !!userRole && !!token,
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // 1 minute
    refetchOnWindowFocus: true,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
}

export function useChatMessages(chatId: string, page: number = 1) {
  const { token } = useAuth();
  const { currentChat } = useChat(); // Get current chat with participants
  
  return useQuery({
    queryKey: chatQueryKeys.messagesPage(chatId, page),
    queryFn: async () => {
      const response = await chatService.getMessages(chatId, page, token || undefined);
      if (!response.isSuccess || !response.data) {
        throw new Error(response.error || 'Failed to fetch messages');
      }
      
      const messages = response.data.data;
      
      // Enrich messages with user data from participants
      if (currentChat?.participants) {
        const enrichedMessages = messages.map(message => {
          const participant = currentChat.participants.find(p => p.id === message.user_id);
          return {
            ...message,
            user: participant || message.user || {
              id: message.user_id,
              name: 'Unknown User',
              type: 'user'
            }
          };
        });
        return enrichedMessages;
      }
      
      return messages;
    },
    enabled: !!chatId && !!token,
    staleTime: 10000,
    refetchOnWindowFocus: false,
    retry: 2,
    retryDelay: 1000,
  });
}

/**
 * Hook to send a message with optimistic updates
 */
export function useSendMessage() {
  const { token, user } = useAuth();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ chatId, message }: { chatId: string; message: SendMessageRequest }) => {
      const response = await chatService.sendMessage(chatId, message, token || undefined);
      if (!response.isSuccess || !response.data) {
        throw new Error(response.error || 'Failed to send message');
      }
      return response.data.data;
    },
    onMutate: async ({ chatId, message }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: chatQueryKeys.messages(chatId) });
      
      // Snapshot the previous value
      const previousMessages = queryClient.getQueryData<Message[]>(chatQueryKeys.messages(chatId));
      
      // Optimistically update the messages cache
      const optimisticMessage: Message = {
        id: `temp-${Date.now()}`,
        chat_id: chatId,
        user_id: user?.id || 'current-user',
        type: message.type,
        message: message.message,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        user: {
          id: user?.id || 'current-user',
          name: user?.name || user?.first_name || 'You',
          type: user?.role || user?.user_type || 'customer',
          email: user?.email,
          avatar: user?.avatar || user?.profile_picture,
        },
      };
      
      queryClient.setQueryData<Message[]>(
        chatQueryKeys.messages(chatId),
        (old) => [...(old || []), optimisticMessage]
      );
      
      return { previousMessages, optimisticMessage };
    },
    onError: (err, { chatId }, context) => {
      // Rollback on error
      if (context?.previousMessages) {
        queryClient.setQueryData(chatQueryKeys.messages(chatId), context.previousMessages);
      }
    },
    onSuccess: (data, { chatId }, context) => {
      console.log('onSuccess called with:', { data, chatId, context });
      
      // Add safety check for context and optimisticMessage
      if (!context || !context.optimisticMessage) {
        console.log('Context or optimisticMessage is undefined, invalidating queries instead');
        queryClient.invalidateQueries({ queryKey: chatQueryKeys.messages(chatId) });
        queryClient.invalidateQueries({ queryKey: chatQueryKeys.lists() });
        return;
      }
      
      // Add safety check for data structure
      if (!data || typeof data !== 'object') {
        console.log('Invalid data structure received, invalidating queries instead');
        queryClient.invalidateQueries({ queryKey: chatQueryKeys.messages(chatId) });
        queryClient.invalidateQueries({ queryKey: chatQueryKeys.lists() });
        return;
      }
      
      // Replace optimistic message with real message
      queryClient.setQueryData<Message[]>(
        chatQueryKeys.messages(chatId),
        (old) => {
          if (!old) {
            console.log('Early return: no old data');
            return old;
          }
          
          return old.map(msg => {
            console.log('msg.id', msg.id);
            console.log('context.optimisticMessage.id', context.optimisticMessage.id);
            if (msg.id === context.optimisticMessage.id) {
              // Ensure the returned data has all required properties
              return {
                ...context.optimisticMessage, // Use optimistic message as base
                ...data, // Override with real data
                id: data.id || context.optimisticMessage.id, // Ensure id exists
              };
            }
            return msg;
          });
        }
      );
      
      // Invalidate chat list to update last message
      queryClient.invalidateQueries({ queryKey: chatQueryKeys.lists() });
    },
    // Remove or modify onSettled to avoid conflicts
    onSettled: (data, error, { chatId }) => {
      // Only invalidate on error, not on success
      if (error) {
        queryClient.invalidateQueries({ queryKey: chatQueryKeys.messages(chatId) });
      }
    },
  });
}

/**
 * Hook to create a new chat
 */
export function useCreateChat() {
  const { token } = useAuth();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ 
      participantIds, 
      type = 'direct', 
      name 
    }: { 
      participantIds: string[]; 
      type?: 'direct' | 'group'; 
      name?: string; 
    }) => {
      const response = await chatService.createChat(
        participantIds.join(','), 
        type, 
        name, 
        token || undefined
      );
      if (!response.isSuccess || !response.data) {
        throw new Error(response.error || 'Failed to create chat');
      }
      return response.data.data;
    },
    onSuccess: (newChat) => {
      // Add new chat to all relevant cache entries
      queryClient.setQueryData<Chat[]>(
        chatQueryKeys.lists(),
        (old) => old ? [newChat, ...old] : [newChat]
      );
      
      // Invalidate chat lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: chatQueryKeys.lists() });
    },
  });
}

/**
 * Hook to mark messages as read
 */
export function useMarkAsRead() {
  const { token } = useAuth();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (chatId: string) => {
      const response = await chatService.markAsRead(chatId, token || undefined);
      if (!response.isSuccess) {
        throw new Error(response.error || 'Failed to mark as read');
      }
      return response.data;
    },
    onSuccess: (data, chatId) => {
      // Update chat list to clear unread count
      queryClient.setQueryData<Chat[]>(
        chatQueryKeys.lists(),
        (old) => old?.map(chat => 
          chat.id === chatId 
            ? { ...chat, unread_count: 0 }
            : chat
        )
      );
    },
  });
}

/**
 * Hook to delete a message
 */
export function useDeleteMessage() {
  const { token } = useAuth();
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ chatId, messageId }: { chatId: string; messageId: string }) => {
      const response = await chatService.deleteMessage(chatId, messageId, token || undefined);
      if (!response.isSuccess) {
        throw new Error(response.error || 'Failed to delete message');
      }
      return response.data;
    },
    onSuccess: (data, { chatId, messageId }) => {
      // Remove message from cache
      queryClient.setQueryData<Message[]>(
        chatQueryKeys.messages(chatId),
        (old) => old?.filter(msg => msg.id !== messageId)
      );
      
      // Invalidate chat list in case this was the last message
      queryClient.invalidateQueries({ queryKey: chatQueryKeys.lists() });
    },
  });
}

/**
 * Hook to prefetch chat messages for better UX
 */
export function usePrefetchChatMessages() {
  const { token } = useAuth();
  const queryClient = useQueryClient();
  
  return (chatId: string) => {
    queryClient.prefetchQuery({
      queryKey: chatQueryKeys.messages(chatId),
      queryFn: async () => {
        const response = await chatService.getMessages(chatId, 1, token || undefined);
        if (!response.isSuccess || !response.data) {
          throw new Error(response.error || 'Failed to prefetch messages');
        }
        return response.data.data;
      },
      staleTime: 10000,
    });
  };
}

/**
 * Hook to invalidate all chat-related queries
 */
export function useInvalidateChats() {
  const queryClient = useQueryClient();
  
  return () => {
    queryClient.invalidateQueries({ queryKey: chatQueryKeys.all });
  };
}