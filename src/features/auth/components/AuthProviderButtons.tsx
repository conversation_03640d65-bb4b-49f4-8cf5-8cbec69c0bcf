
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { useAuth } from '../hooks/useAuth';
import { Mail } from 'lucide-react';
import { GoogleLogin } from '@react-oauth/google';
import {apiService} from "@/services/api.ts";
import { useNavigate } from 'react-router-dom';
import useSignIn from 'react-auth-kit/hooks/useSignIn';
import {toast} from "@/hooks/use-toast.ts";
import {useRoles} from "@/features/auth/context/RoleContext.tsx";

interface AuthProviderButtonsProps {
    redirectUrl?: string;
    activeTab?: 'customer' | 'professional';
}


interface resType {
  success: boolean;
  access_token: string;
  user: {
    id: number;
    name: string;
    email: string;
    phone: null;
    status: number;
    role: {
      id: number;
      name: string;
      guard_name: string;
      system_reserve: number;
      created_at: string;
      updated_at: string;
      pivot: { model_type: string; model_id: number; role_id: number }
    };
    total_booking: number;
    last_active: string;
    created_at: string;
    updated_at: string
  };
  error: null,
  status: number,
  isSuccess: boolean
}

const AuthProviderButtons: React.FC<AuthProviderButtonsProps> = ({
  redirectUrl,
  activeTab = 'customer'
}) => {
  const [authMethod, setAuthMethod] = useState<string | null>(null);
  const { authenticateWithProvider, authenticateWithGoogle, isLoading } = useAuth();
  const navigate = useNavigate();
  const signIn = useSignIn();
  const { refreshRoles, getDashboardRoute, roles } = useRoles();

  const handleProviderAuth = (provider: string) => {
    setAuthMethod(provider);
    if (provider !== 'email') {
      authenticateWithProvider(provider);
    }
  };

  const showErrorToast = () => {
    toast({
      title: 'Error',
      description: 'Login Failed. Please try again later.',
      variant: 'destructive',
    });
  };
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 gap-3">
        <GoogleLogin
            onSuccess={async (credentialResponse) => {
              const { credential: id_token } = credentialResponse;

              if (!id_token) {
                showErrorToast();
                return;
              }

              // Get the appropriate role_id based on activeTab
              const roleId = roles.find(role => 
                activeTab === 'customer' ? role.name === 'user' : 
                activeTab === 'professional' ? role.name === 'provider' : 
                role.name === 'user'
              )?.id;

              try {
                const result = await authenticateWithGoogle(id_token, roleId, redirectUrl);

                if (!result.success) {
                  showErrorToast();
                } else {
                  toast({
                    title: 'Success',
                    description: 'Login Successful!',
                    className: 'bg-green-400 text-white',
                  });
                }
              } catch (error) {
                showErrorToast();
              }
            }}
            onError={() => {
              console.log('Google Login Failed');
              showErrorToast();
            }}
        />

        {/*<Button*/}
        {/*  variant="outline"*/}
        {/*  className="bg-black hover:bg-gray-900 text-white border border-gray-800 flex items-center justify-center gap-2"*/}
        {/*  onClick={() => handleProviderAuth('Apple')}*/}
        {/*  disabled={isLoading}*/}
        {/*>*/}
        {/*  <svg className="h-5 w-5" viewBox="0 0 24 24" fill="currentColor">*/}
        {/*    <path d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701z" />*/}
        {/*  </svg>*/}
        {/*  Continue with Apple*/}
        {/*</Button>*/}

        <Button
          variant="outline" 
          className="bg-white hover:bg-gray-50 text-black border border-gray-300 flex items-center justify-center gap-2"
          onClick={() => handleProviderAuth('email')}
          disabled={isLoading}
        >
          <Mail className="h-5 w-5" />
          Continue with Email
        </Button>
      </div>

      {authMethod !== 'email' && (
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <Separator />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-background px-2 text-muted-foreground">
              Or continue with email
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuthProviderButtons;
