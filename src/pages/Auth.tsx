
import React, { useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Layout } from '@/components/Layout';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { useUIHelpers } from '@/hooks/use-ui-helpers';

// Import custom hooks and components
import { useAuth } from '@/features/auth/hooks/useAuth';
import AccountTypeSelection from '@/features/auth/components/AccountTypeSelection';
import AuthenticatedUser from '@/features/auth/components/AuthenticatedUser';
import LoginForm from '@/features/auth/components/LoginForm';
import SignupForm from '@/features/auth/components/SignupForm';

const Auth = () => {
  const [searchParams] = useSearchParams();
  const initialMode = searchParams.get('mode') || 'signup';
  const redirectUrl = searchParams.get('redirectUrl') || searchParams.get('redirect') || '';

  // Check if this is a Stripe provider signup flow
  const redirectToStripe = searchParams.get('redirectToStripe');
  const isStripeProviderFlow = redirectToStripe === 'true';

  const [mode, setMode] = useState<'login' | 'signup'>(initialMode === 'login' ? 'login' : 'signup');
  const [accountType, setAccountType] = useState<'customer' | 'professional'>(
    isStripeProviderFlow ? 'professional' : 'customer'
  );
  const [showForm, setShowForm] = useState(isStripeProviderFlow);
  const { isAuthenticated } = useAuth();
  const { isMobile } = useUIHelpers();

  const handleSelectAccountType = (type: 'customer' | 'professional') => {
    setAccountType(type);
    setShowForm(true);
  };

  const handleModeChange = () => {
    setMode(mode === 'login' ? 'signup' : 'login');
    setShowForm(false);
  };

  const handleBackToAccountSelection = () => {
    // Don't allow going back if this is a Stripe provider flow
    if (isStripeProviderFlow) {
      return;
    }
    setShowForm(false);
  };

  React.useEffect(() => {
    if(isAuthenticated && redirectUrl) {
      window.location.href = redirectUrl;
    }
  }, [isAuthenticated,redirectUrl]);

  // If already authenticated, redirect to the redirectUrl if provided, otherwise show profile
  if (isAuthenticated) {
    // If redirectUrl is provided, navigate to it
    if (redirectUrl) {
      // Use effect to navigate after render

      // Return a loading state while redirecting
      return (
        <Layout>
          <div className="min-h-screen px-4 pt-28 pb-16 bg-gradient-to-b from-background to-muted/30 flex items-center justify-center">
            <div className="text-center">
              <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite] mb-4"></div>
              <p className="text-lg font-medium">Redirecting to your destination...</p>
            </div>
          </div>
        </Layout>
      );
    }
    // Otherwise show the authenticated user profile
    return (
      <Layout>
        <AuthenticatedUser />
      </Layout>
    );
  }

  return (
    <Layout>
      <div className={cn(
        "min-h-screen px-4 bg-gradient-to-b from-background to-muted/30",
        isMobile ? "pt-20 pb-12" : "pt-28 pb-16"
      )}>
        <div className="max-w-6xl mx-auto">
          <div className={cn("text-center", isMobile ? "mb-5" : "mb-8")}>
            <h1 className={cn("font-bold tracking-tight mb-2", isMobile ? "text-2xl" : "text-3xl md:text-4xl")}>
              {isStripeProviderFlow ? "Start Your Provider Journey" : "Join JobON"}
            </h1>
            <p className={cn("text-muted-foreground max-w-lg mx-auto", isMobile ? "text-sm" : "")}>
              {isStripeProviderFlow
                ? "Create your provider account and get access to your selected plan"
                : mode === 'signup'
                ? "Create an account to connect with professionals or offer your services"
                : "Welcome back! Log in to your account"}
            </p>
          </div>

          {/* Mode selection buttons */}
          <div className={cn("flex justify-center gap-4", isMobile ? "mb-5" : "mb-8")}>
            <Button
              variant={mode === 'signup' ? "default" : "outline"}
              className={cn(
                "px-6 py-2 rounded-full",
                mode === 'signup' ? "shadow-md" : ""
              )}
              onClick={() => setMode('signup')}
            >
              Sign Up
            </Button>
            <Button
              variant={mode === 'login' ? "default" : "outline"}
              className={cn(
                "px-6 py-2 rounded-full",
                mode === 'login' ? "shadow-md" : ""
              )}
              onClick={() => setMode('login')}
            >
              Login
            </Button>
          </div>

          {/* Main content area */}
          {mode === 'signup' && !showForm ? (
            <AccountTypeSelection onSelect={handleSelectAccountType} isMobile={isMobile} />
          ) : mode === 'login' ? (
            <div className="max-w-md mx-auto">
              <Card className="border-none shadow-lg">
                <LoginForm
                  onModeChange={handleModeChange}
                  redirectUrl={redirectUrl}
                  activeTab={accountType}
                />
              </Card>
            </div>
          ) : (
            <div className="max-w-md mx-auto">
              <Card className="border-none shadow-lg">
                <SignupForm
                  accountType={accountType}
                  onModeChange={handleModeChange}
                  onBack={handleBackToAccountSelection}
                  redirectUrl={redirectUrl}
                />
              </Card>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};

export default Auth;
