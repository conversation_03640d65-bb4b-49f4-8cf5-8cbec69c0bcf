import React from 'react';
import { CustomerMessages as CustomerMessagesComponent } from '@/components/customer/CustomerMessages';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { ChatProvider } from '@/contexts/ChatContext';

/**
 * Customer Messages Page
 * 
 * Dedicated page for customer messaging functionality using the Universal Chat component.
 */
const CustomerMessages: React.FC = () => {
  return (
    <ChatProvider>
      <CustomerLayout>
        <div className="h-[calc(100vh-80px)]">
          <CustomerMessagesComponent />
        </div>
      </CustomerLayout>
    </ChatProvider>
  );
};

export default CustomerMessages;
