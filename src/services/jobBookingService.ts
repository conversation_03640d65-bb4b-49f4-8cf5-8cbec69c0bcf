import { ApiResponse, apiService } from './api';
import { ProviderJobBookingDetailsResponse } from '../types/jobs';

// Job Booking interfaces
export interface JobBooking {
  jobId: string;
  projectCode: string;
  createdAt: string;
  status: string;
  jobType: string;
  property: {
    type: string;
  };
  service: {
    category: string;
    tasks: string[];
    customTask: string | null;
  };
  description: string | null;
  schedule: {
    date: string;
    timePreference: string;
    frequency: string;
    recurringFrequency: string | null;
  };
  budget: string | null;
  location: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
  };
  contact: {
    fullName: string;
    email: string;
    phone: string;
  };
  assets: any[];
  user: any | null;
}

export interface JobBookingResponse {
  data: JobBooking[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
}

export interface JobBookingProvider {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  rating?: number;
  reviewCount?: number;
  status?: string;
}

export interface JobBookingProviderResponse {
  data: JobBookingProvider[];
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    per_page: number;
    to: number;
    total: number;
  };
}

// Job Booking service with API functions
export const jobBookingService = {
  // Get job bookings by user ID
  getJobBookingsByUserId: (
    userId: string,
    page: number = 1,
    perPage: number = 10,
    token?: string
  ): Promise<ApiResponse<JobBookingResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<JobBookingResponse>(`/api/job-bookings?user_id=${userId}&page=${page}&per_page=${perPage}`, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },

  // Get all job bookings (admin only)
  getAllJobBookings: (
    page: number = 1,
    perPage: number = 10,
    search?: string,
    token?: string
  ): Promise<ApiResponse<JobBookingResponse>> => {
    let endpoint = `/api/job-bookings?page=${page}&per_page=${perPage}`;

    if (search) {
      endpoint += `&search=${encodeURIComponent(search)}`;
    }

    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<JobBookingResponse>(endpoint, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },

  // Get providers for a job booking
  getProvidersForJobBooking: (
    jobId: string,
    page: number = 1,
    perPage: number = 10,
    token?: string
  ): Promise<ApiResponse<JobBookingProviderResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<JobBookingProviderResponse>(`/api/job-bookings/${jobId}/providers?page=${page}&per_page=${perPage}`, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },

  // Get provider specific job booking details
  getProviderJobBookingDetails: (
    jobId: string,
    token?: string
  ): Promise<ApiResponse<ProviderJobBookingDetailsResponse>> => {
    const headers: Record<string, string> = {};
    if (token) {
      headers['Authorization'] = token;
    }

    return apiService<ProviderJobBookingDetailsResponse>(`/api/provider/job-bookings/${jobId}`, {
      method: 'GET',
      requiresAuth: true,
      headers,
    });
  },
};
