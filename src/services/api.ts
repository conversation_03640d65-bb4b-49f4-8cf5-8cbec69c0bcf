import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { BASE_API_URL } from '@/lib/constants';

interface ApiOptions {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    headers?: Record<string, string>;
    body?: unknown;
    requiresAuth?: boolean;
    includeCredentials?: boolean;
    externalUrl?: string;
    token?: string;
}

export interface ApiResponse<T> {
    data: T | null;
    error: string | null;
    status: number;
    isSuccess: boolean;
    success?: boolean; // For endpoints that return success: true/false
}

// Create axios instance with default configuration
const axiosInstance: AxiosInstance = axios.create({
    baseURL: BASE_API_URL,
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
    withCredentials: false // Enable sending cookies with requests
});

// Request interceptor for adding auth token
axiosInstance.interceptors.request.use(
    (config) => {
        // If headers already contain Authorization, use that
        if (config.headers && config.headers.Authorization) {
            return config;
        }

        // The auth token will be sent automatically via cookies
        // since we're using withCredentials: true
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Response interceptor for handling errors
axiosInstance.interceptors.response.use(
    (response) => response,
    (error: AxiosError) => {
        // Handle token expiration or other auth errors here if needed
        if (error.response?.status === 401) {
            // Handle unauthorized error (e.g., redirect to login)
            console.error('Authentication error:', error);
        }
        return Promise.reject(error);
    }
);

/**
 * Fetch roles from the API
 * @returns Promise with roles data
 */
export const fetchRoles = async (): Promise<ApiResponse<any>> => {
    try {
        const response = await axiosInstance.get('/api/roles-public');
        return {
            data: response.data,
            error: null,
            status: response.status,
            isSuccess: true
        };
    } catch (error: any) {
        return {
            data: null,
            error: error.message || 'Failed to fetch roles',
            status: error.response?.status || 0,
            isSuccess: false
        };
    }
};

/**
 * Generic API service function
 * @param endpoint API endpoint
 * @param options Request options
 * @returns Promise with API response
 */
export async function apiService<T = never>(
    endpoint: string,
    options: ApiOptions = {}
): Promise<ApiResponse<T>> {
    const {
        method = 'GET',
        headers = {},
        body,
        requiresAuth = false,
        includeCredentials = false, // Default to true to match axiosInstance default
        externalUrl,
    } = options;

    // Configure request
    const config: AxiosRequestConfig = {
        url: externalUrl || endpoint,
        method,
        headers,
        withCredentials: includeCredentials // Use the includeCredentials parameter
    };

    // Add request body for non-GET requests
    if (body && method !== 'GET') {
        config.data = body;
    }

    try {
        // Make the request
        const response: AxiosResponse = await axiosInstance(config);

        return {
            data: response.data,
            error: null,
            status: response.status,
            isSuccess: true,
        };
    } catch (error: unknown) {
        console.error(error);

        if (axios.isAxiosError(error)) {
            return {
                data: null,
                error: error.response?.data?.message || error.message,
                status: error.response?.status || 0,
                isSuccess: false,
            };
        }

        return {
            data: null,
            error: error instanceof Error ? error.message : 'An unknown error occurred',
            status: 0,
            isSuccess: false,
        };
    }
}
