import { apiService } from "./api";
import { ProviderPlan } from "@/components/admin/provider-tier/schemas";

// Response interfaces
export interface PlansResponse {
  success: boolean;
  message?: string;
  data?: {
    data: ProviderPlan[];
    total: number;
    page: number;
    limit: number;
  };
}

export interface PlanResponse {
  success: boolean;
  message?: string;
  data?: ProviderPlan;
}

// Request interfaces
export interface CreatePlanRequest {
  name: string;
  price: number;
  description: string;
  commission: string;
  features: Array<{
    included: boolean;
    text: string;
  }>;
}

export interface UpdatePlanRequest extends CreatePlanRequest {
  id: string;
}

// Provider Plan Service
export const planService = {
  /**
   * Get all subscription plans with pagination
   */
  getPlans: async (
    page: number = 1,
    limit: number = 10,
    token?: string
  ): Promise<PlansResponse> => {
    try {
      // Call the actual API endpoint to get subscription plans
      const headers: Record<string, string> = {};
      if (token) {
        // Ensure token has Bearer prefix
        headers['Authorization'] = token.startsWith('Bearer ') ? token : `Bearer ${token}`;
      }

      const response = await apiService<{
        data: any[];
        total: number;
        page: number;
        limit: number;
      }>(
        `/api/subscription/plans?page=${page}&limit=${limit}`,
        {
          method: 'GET',
          headers,
          requiresAuth: true,
        }
      );

      // Transform the API response to match our interface if needed
      // The API returns: name, max_services, max_addresses, max_servicemen, max_service_packages, price, status, created_by, duration, description
      // We need to map this to our ProviderPlan interface

      if (response.isSuccess && response.data) {
        // Transform the data to match our ProviderPlan interface
        const transformedData = response.data.data.map((plan: any) => ({
          id: plan.id || String(plan._id),
          name: plan.name,
          price: plan.price,
          description: plan.description,
          commission: plan.commission || "Standard", // Default if not provided
          // Create features based on the plan attributes
          features: [
            { included: plan.max_services > 0, text: `Up to ${plan.max_services} services` },
            { included: plan.max_addresses > 0, text: `Up to ${plan.max_addresses} addresses` },
            { included: plan.max_servicemen > 0, text: `Up to ${plan.max_servicemen} servicemen` },
            { included: plan.max_service_packages > 0, text: `Up to ${plan.max_service_packages} service packages` },
            { included: plan.status === "active", text: "Active plan" },
            { included: true, text: `${plan.duration || "Monthly"} billing cycle` },
          ],
          duration: plan.duration,
          // Include Stripe integration fields
          stripe_price_id: plan.stripe_price_id,
          stripe_product_id: plan.stripe_product_id,
        }));
        
        return {
          success: true,
          data: {
            data: transformedData,
            total: response.data.total,
            page: response.data.page,
            limit: response.data.limit,
          },
        };
      }
      
      return response as PlansResponse;
    } catch (error) {
      console.error("Error fetching plans:", error);
      return {
        success: false,
        message: "Failed to fetch plans",
      };
    }
  },

  /**
   * Get a specific plan by ID
   */
  getPlan: async (id: string, token?: string): Promise<PlanResponse> => {
    try {
      // Call the actual API endpoint to get a specific plan
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }

      const response = await apiService<any>(`/api/subscription/plans/${id}`, {
        method: 'GET',
        headers,
        requiresAuth: true,
      });

      // Transform the API response to match our interface if needed
      if (response.isSuccess && response.data) {
        const plan = response.data;
        
        // Transform the data to match our ProviderPlan interface
        const transformedPlan: ProviderPlan = {
          id: plan.id || String(plan._id),
          name: plan.name,
          price: plan.price,
          description: plan.description,
          commission: plan.commission || "Standard", // Default if not provided
          // Create features based on the plan attributes
          features: [
            { included: plan.max_services > 0, text: `Up to ${plan.max_services} services` },
            { included: plan.max_addresses > 0, text: `Up to ${plan.max_addresses} addresses` },
            { included: plan.max_servicemen > 0, text: `Up to ${plan.max_servicemen} servicemen` },
            { included: plan.max_service_packages > 0, text: `Up to ${plan.max_service_packages} service packages` },
            { included: plan.status === "active", text: "Active plan" },
            { included: true, text: `${plan.duration || "Monthly"} billing cycle` },
          ],
        };
        
        return {
          success: true,
          data: transformedPlan,
        };
      }
      
      return response as PlanResponse;
    } catch (error) {
      console.error(`Error fetching plan ${id}:`, error);
      return {
        success: false,
        message: "Failed to fetch plan",
      };
    }
  },

  /**
   * Create a new subscription plan
   */
  createPlan: async (
    planData: CreatePlanRequest,
    token?: string
  ): Promise<PlanResponse> => {
    try {
      // Transform our internal plan data format to match the API's expected format
      const apiPlanData = {
        name: planData.name,
        price: planData.price,
        description: planData.description,
        commission: planData.commission,
        // Extract feature information to set max values
        max_services: 0,
        max_addresses: 0,
        max_servicemen: 0,
        max_service_packages: 0,
        status: "active",
        duration: "Monthly" // Default duration
      };
      
      // Call the actual API endpoint to create a new plan
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }

      const response = await apiService<any>('/api/subscription/plans', {
        method: 'POST',
        body: apiPlanData,
        headers,
        requiresAuth: true,
      });

      if (response.isSuccess && response.data) {
        // Transform the response data to match our ProviderPlan interface
        const transformedPlan: ProviderPlan = {
          id: response.data.id || String(response.data._id),
          name: response.data.name,
          price: response.data.price,
          description: response.data.description,
          commission: response.data.commission || planData.commission,
          features: planData.features, // Use the features from the request
        };
        
        return {
          success: true,
          message: "Plan created successfully",
          data: transformedPlan,
        };
      }
      
      return response as PlanResponse;
    } catch (error) {
      console.error("Error creating plan:", error);
      return {
        success: false,
        message: "Failed to create plan",
      };
    }
  },

  /**
   * Update an existing subscription plan
   */
  updatePlan: async (
    planData: UpdatePlanRequest,
    token?: string
  ): Promise<PlanResponse> => {
    try {
      // Transform our internal plan data format to match the API's expected format
      const apiPlanData = {
        name: planData.name,
        price: planData.price,
        description: planData.description,
        commission: planData.commission,
        // Extract feature information to set max values if needed
        // We're keeping the existing values for these fields
        status: "active",
      };
      
      // Call the actual API endpoint to update the plan
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }

      const response = await apiService<any>(
        `/api/subscription/plans/${planData.id}`,
        {
          method: 'PUT',
          body: apiPlanData,
          headers,
          requiresAuth: true,
        }
      );

      if (response.isSuccess && response.data) {
        // Transform the response data to match our ProviderPlan interface
        const transformedPlan: ProviderPlan = {
          id: response.data.id || String(response.data._id),
          name: response.data.name,
          price: response.data.price,
          description: response.data.description,
          commission: response.data.commission || planData.commission,
          features: planData.features, // Use the features from the request
        };
        
        return {
          success: true,
          message: "Plan updated successfully",
          data: transformedPlan,
        };
      }
      
      return response as PlanResponse;
    } catch (error) {
      console.error(`Error updating plan ${planData.id}:`, error);
      return {
        success: false,
        message: "Failed to update plan",
      };
    }
  },

  /**
   * Delete a subscription plan
   */
  deletePlan: async (id: string, token?: string): Promise<{ success: boolean; message?: string }> => {
    try {
      // Call the actual API endpoint to delete the plan
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }

      const response = await apiService<any>(`/api/subscription/plans/${id}`, {
        method: 'DELETE',
        headers,
        requiresAuth: true,
      });

      return {
        success: response.isSuccess,
        message: response.error || "Plan deleted successfully",
      };
    } catch (error) {
      console.error(`Error deleting plan ${id}:`, error);
      return {
        success: false,
        message: "Failed to delete plan",
      };
    }
  },

  /**
   * Assign a plan to a provider
   */
  assignPlanToProvider: async (
    providerId: string,
    planId: string,
    notes?: string,
    token?: string,
    duration?: 'monthly' | 'yearly'
  ): Promise<{ success: boolean; message?: string; subscription?: any }> => {
    try {
      // Call the actual API endpoint to assign a plan to a provider
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }

      const response = await apiService<{
        success?: boolean;
        message?: string;
        subscription?: any;
      }>('/api/admin/provider/update-tier', {
        method: 'POST',
        body: {
          provider_id: providerId,
          plan_id: planId,
          duration: duration || undefined,
          notes
        },
        headers,
        requiresAuth: true,
      });

      if (response.isSuccess && response.data) {
        return {
          success: response.data.success ?? true,
          message: response.data.message || "Provider tier plan updated successfully",
          subscription: response.data.subscription
        };
      }

      return {
        success: false,
        message: response.error || "Failed to update provider tier",
      };
    } catch (error) {
      console.error(`Error updating provider tier ${providerId}:`, error);
      return {
        success: false,
        message: "Failed to update provider tier",
      };
    }
  },

  /**
   * Get the current plan for a provider
   */
  getProviderPlan: async (
    providerId: string,
    token?: string
  ): Promise<PlanResponse & { startDate?: string; endDate?: string; isActive?: boolean; notes?: string }> => {
    try {
      // Call the actual API endpoint to get the provider's current plan
      const headers: Record<string, string> = {};
      if (token) {
        headers['Authorization'] = token;
      }

      const response = await apiService<any>(`/api/subscription/provider-plans/${providerId}`, {
        method: 'GET',
        headers,
        requiresAuth: true,
      });

      if (response.isSuccess && response.data) {
        const providerPlan = response.data;
        const plan = providerPlan.plan;
        
        // Transform the data to match our ProviderPlan interface
        const transformedPlan: ProviderPlan = {
          id: plan.id || String(plan._id),
          name: plan.name,
          price: plan.price,
          description: plan.description,
          commission: plan.commission || "Standard", // Default if not provided
          // Create features based on the plan attributes
          features: [
            { included: plan.max_services > 0, text: `Up to ${plan.max_services} services` },
            { included: plan.max_addresses > 0, text: `Up to ${plan.max_addresses} addresses` },
            { included: plan.max_servicemen > 0, text: `Up to ${plan.max_servicemen} servicemen` },
            { included: plan.max_service_packages > 0, text: `Up to ${plan.max_service_packages} service packages` },
            { included: plan.status === "active", text: "Active plan" },
            { included: true, text: `${plan.duration || "Monthly"} billing cycle` },
          ],
        };
        
        return {
          success: true,
          data: transformedPlan,
          startDate: providerPlan.start_date,
          endDate: providerPlan.end_date,
          isActive: providerPlan.is_active,
          notes: providerPlan.notes,
        };
      }
      
      return response as PlanResponse & { startDate?: string; endDate?: string; isActive?: boolean; notes?: string };
    } catch (error) {
      console.error(`Error fetching provider plan for ${providerId}:`, error);
      return {
        success: false,
        message: "Failed to fetch provider plan",
      };
    }
  },
};