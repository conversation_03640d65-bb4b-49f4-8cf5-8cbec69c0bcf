import React from 'react';
import { Route, Routes, Navigate, Outlet } from 'react-router-dom';
import RequireAuth from '@auth-kit/react-router/RequireAuth';
import { useAuth } from '@/features/auth/hooks/useAuth';

// Import customer pages
import CustomerDashboard from '@/pages/CustomerDashboard';
import CustomerMessages from '@/pages/CustomerMessages';

// Import job management components
import { JobManage } from '@/components/customer/job-management/JobManage';
import { JobBids } from '@/components/customer/job-management/JobBids';

// Import the customer layout
import CustomerLayout from '@/components/customer/CustomerLayout';

/**
 * Customer Routes Component
 *
 * This component handles all routes that should only be accessible to customers.
 * It uses RequireAuth from @auth-kit/react-router to protect routes and redirects
 * non-customers to the appropriate page.
 */
const CustomerRoutes: React.FC = () => {
  const { isCustomer, rolesLoading } = useAuth();

  // Customer check component
  const CustomerCheck = () => {
    // If roles are still loading, don't redirect yet
    if (rolesLoading) {
      // You could return a loading spinner here if desired
      return <div></div>;
    }

    // Once roles are loaded, check if user is a customer
    return isCustomer ? <Outlet /> : <Navigate to="/" replace />;
  };

  return (
    <Routes>
      <Route element={
        <RequireAuth fallbackPath="/auth">
          <CustomerCheck />
        </RequireAuth>
      }>
        {/* Apply the CustomerLayout to all customer routes */}
        <Route element={<CustomerLayout />}>
          {/* Customer routes */}
          <Route path="/dashboard" element={<CustomerDashboard />} />
          <Route path="/dashboard/*" element={<CustomerDashboard />} />
          <Route path="/messages" element={<CustomerMessages />} />
          <Route path="/messages/:chatId" element={<CustomerMessages />} />

          {/* Job management routes */}
          <Route path="/jobs/:jobId/manage" element={<JobManage />} />
          <Route path="/jobs/:jobId/bids" element={<JobBids />} />

          {/* Catch-all route for customer section */}
          <Route path="*" element={<Navigate to="/customer/dashboard" replace />} />
        </Route>
      </Route>
    </Routes>
  );
};

export default CustomerRoutes;
