{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "build:with-sitemap": "node scripts/build-with-sitemap.mjs", "lint": "eslint .", "preview": "vite preview", "generate-sitemap": "node scripts/generate-sitemap.mjs", "generate-dynamic-sitemap": "node scripts/generate-dynamic-sitemap-v2.cjs", "update-sitemap": "node scripts/update-sitemap.mjs", "auto-generate-sitemap": "node scripts/auto-generate-sitemap.js", "sitemap": "node scripts/generate-sitemap.mjs", "generate-wp-sitemap": "node scripts/generate-wp-sitemap.mjs", "sitemap-api": "node scripts/sitemap-api.mjs", "prebuild": "npm run generate-dynamic-sitemap", "typecheck": "tsc --noEmit"}, "dependencies": {"@auth-kit/react-router": "^3.1.3", "@hookform/resolvers": "^3.9.0", "@mapbox/mapbox-sdk": "^0.16.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@react-oauth/google": "^0.12.2", "@rollup/rollup-linux-x64-gnu": "^4.41.1", "@smastrom/react-rating": "^1.5.0", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.56.2", "@types/mapbox__mapbox-sdk": "^0.16.3", "@types/mapbox-gl": "^3.4.1", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.4.1", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.0.0", "embla-carousel-react": "^8.0.0", "framer-motion": "^12.9.2", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "lucide-react": "^0.501.0", "mapbox-gl": "^3.12.0", "react": "^18.3.1", "react-auth-kit": "3.1.3", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.54.2", "react-pdf": "^7.7.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "zipcodes": "^8.0.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.8.1", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.8", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^6.3.4"}, "engines": {"node": ">=18.0.0"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}